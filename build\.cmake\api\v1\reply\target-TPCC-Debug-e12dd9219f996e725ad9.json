{"artifacts": [{"path": "Source/M Control Center.exe"}, {"path": "Source/M Control Center.pdb"}], "backtrace": 4, "backtraceGraph": {"commands": ["add_executable", "_qt_internal_create_executable", "qt6_add_executable", "qt_add_executable", "install", "target_link_libraries", "set_target_properties", "include", "find_package", "find_dependency", "_qt_internal_find_qt_dependencies", "add_dependencies", "qt6_add_ui", "qt_add_ui", "target_compile_definitions", "include_directories", "add_include", "target_include_directories", "target_sources"], "files": ["D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreMacros.cmake", "Source/CMakeLists.txt", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6Network/Qt6NetworkTargets.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6Network/Qt6NetworkConfig.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6/Qt6Config.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6Charts/Qt6ChartsTargets.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6Charts/Qt6ChartsConfig.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsTargets.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsConfig.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreTargets.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreConfig.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateTargets.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateConfig.cmake", "D:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreDependencies.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6GuiTargets.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6GuiConfig.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsDependencies.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsMacros.cmake"], "nodes": [{"file": 1}, {"command": 3, "file": 1, "line": 85, "parent": 0}, {"command": 2, "file": 0, "line": 938, "parent": 1}, {"command": 1, "file": 0, "line": 639, "parent": 2}, {"command": 0, "file": 0, "line": 688, "parent": 3}, {"command": 4, "file": 1, "line": 160, "parent": 0}, {"command": 5, "file": 1, "line": 147, "parent": 0}, {"command": 8, "file": 1, "line": 81, "parent": 0}, {"file": 4, "parent": 7}, {"command": 8, "file": 4, "line": 212, "parent": 8}, {"file": 3, "parent": 9}, {"command": 7, "file": 3, "line": 55, "parent": 10}, {"file": 2, "parent": 11}, {"command": 6, "file": 2, "line": 61, "parent": 12}, {"command": 8, "file": 4, "line": 212, "parent": 8}, {"file": 6, "parent": 14}, {"command": 7, "file": 6, "line": 55, "parent": 15}, {"file": 5, "parent": 16}, {"command": 6, "file": 5, "line": 61, "parent": 17}, {"command": 8, "file": 4, "line": 212, "parent": 8}, {"file": 8, "parent": 19}, {"command": 7, "file": 8, "line": 55, "parent": 20}, {"file": 7, "parent": 21}, {"command": 6, "file": 7, "line": 61, "parent": 22}, {"command": 5, "file": 0, "line": 640, "parent": 2}, {"command": 8, "file": 4, "line": 212, "parent": 8}, {"file": 10, "parent": 25}, {"command": 7, "file": 10, "line": 57, "parent": 26}, {"file": 9, "parent": 27}, {"command": 6, "file": 9, "line": 61, "parent": 28}, {"command": 7, "file": 10, "line": 45, "parent": 26}, {"file": 15, "parent": 30}, {"command": 10, "file": 15, "line": 46, "parent": 31}, {"command": 9, "file": 14, "line": 137, "parent": 32}, {"command": 8, "file": 13, "line": 76, "parent": 33}, {"file": 12, "parent": 34}, {"command": 7, "file": 12, "line": 55, "parent": 35}, {"file": 11, "parent": 36}, {"command": 6, "file": 11, "line": 61, "parent": 37}, {"command": 6, "file": 11, "line": 83, "parent": 37}, {"command": 7, "file": 8, "line": 43, "parent": 20}, {"file": 18, "parent": 40}, {"command": 10, "file": 18, "line": 45, "parent": 41}, {"command": 9, "file": 14, "line": 137, "parent": 42}, {"command": 8, "file": 13, "line": 76, "parent": 43}, {"file": 17, "parent": 44}, {"command": 7, "file": 17, "line": 55, "parent": 45}, {"file": 16, "parent": 46}, {"command": 6, "file": 16, "line": 61, "parent": 47}, {"command": 13, "file": 1, "line": 91, "parent": 0}, {"command": 12, "file": 19, "line": 340, "parent": 49}, {"command": 11, "file": 19, "line": 325, "parent": 50}, {"command": 14, "file": 1, "line": 156, "parent": 0}, {"command": 16, "file": 1, "line": 51, "parent": 0}, {"command": 15, "file": 1, "line": 16, "parent": 53}, {"command": 16, "file": 1, "line": 17, "parent": 53}, {"command": 15, "file": 1, "line": 16, "parent": 55}, {"command": 15, "file": 1, "line": 16, "parent": 55}, {"command": 15, "file": 1, "line": 16, "parent": 55}, {"command": 16, "file": 1, "line": 17, "parent": 55}, {"command": 15, "file": 1, "line": 16, "parent": 59}, {"command": 15, "file": 1, "line": 16, "parent": 59}, {"command": 15, "file": 1, "line": 16, "parent": 55}, {"command": 15, "file": 1, "line": 16, "parent": 55}, {"command": 15, "file": 1, "line": 16, "parent": 55}, {"command": 15, "file": 1, "line": 16, "parent": 55}, {"command": 15, "file": 1, "line": 16, "parent": 55}, {"command": 15, "file": 1, "line": 16, "parent": 55}, {"command": 15, "file": 1, "line": 16, "parent": 55}, {"command": 15, "file": 1, "line": 16, "parent": 55}, {"command": 16, "file": 1, "line": 17, "parent": 55}, {"command": 15, "file": 1, "line": 16, "parent": 70}, {"command": 15, "file": 1, "line": 16, "parent": 70}, {"command": 15, "file": 1, "line": 16, "parent": 70}, {"command": 15, "file": 1, "line": 16, "parent": 70}, {"command": 15, "file": 1, "line": 16, "parent": 55}, {"command": 15, "file": 1, "line": 16, "parent": 53}, {"command": 16, "file": 1, "line": 17, "parent": 53}, {"command": 15, "file": 1, "line": 16, "parent": 77}, {"command": 16, "file": 1, "line": 17, "parent": 77}, {"command": 15, "file": 1, "line": 16, "parent": 79}, {"command": 15, "file": 1, "line": 16, "parent": 77}, {"command": 16, "file": 1, "line": 17, "parent": 77}, {"command": 15, "file": 1, "line": 16, "parent": 82}, {"command": 15, "file": 1, "line": 16, "parent": 77}, {"command": 15, "file": 1, "line": 16, "parent": 77}, {"command": 16, "file": 1, "line": 17, "parent": 77}, {"command": 15, "file": 1, "line": 16, "parent": 86}, {"command": 15, "file": 1, "line": 16, "parent": 77}, {"command": 16, "file": 1, "line": 17, "parent": 77}, {"command": 15, "file": 1, "line": 16, "parent": 89}, {"command": 15, "file": 1, "line": 16, "parent": 89}, {"command": 15, "file": 1, "line": 16, "parent": 89}, {"command": 15, "file": 1, "line": 16, "parent": 77}, {"command": 16, "file": 1, "line": 17, "parent": 77}, {"command": 15, "file": 1, "line": 16, "parent": 94}, {"command": 15, "file": 1, "line": 16, "parent": 94}, {"command": 15, "file": 1, "line": 16, "parent": 94}, {"command": 15, "file": 1, "line": 16, "parent": 94}, {"command": 15, "file": 1, "line": 16, "parent": 94}, {"command": 15, "file": 1, "line": 16, "parent": 94}, {"command": 15, "file": 1, "line": 16, "parent": 77}, {"command": 16, "file": 1, "line": 17, "parent": 77}, {"command": 15, "file": 1, "line": 16, "parent": 102}, {"command": 15, "file": 1, "line": 16, "parent": 77}, {"command": 15, "file": 1, "line": 16, "parent": 77}, {"command": 16, "file": 1, "line": 17, "parent": 77}, {"command": 15, "file": 1, "line": 16, "parent": 106}, {"command": 15, "file": 1, "line": 16, "parent": 77}, {"command": 16, "file": 1, "line": 17, "parent": 77}, {"command": 15, "file": 1, "line": 16, "parent": 109}, {"command": 15, "file": 1, "line": 16, "parent": 109}, {"command": 15, "file": 1, "line": 16, "parent": 109}, {"command": 15, "file": 1, "line": 16, "parent": 109}, {"command": 16, "file": 1, "line": 17, "parent": 109}, {"command": 15, "file": 1, "line": 16, "parent": 114}, {"command": 15, "file": 1, "line": 16, "parent": 114}, {"command": 15, "file": 1, "line": 16, "parent": 114}, {"command": 15, "file": 1, "line": 16, "parent": 114}, {"command": 15, "file": 1, "line": 16, "parent": 77}, {"command": 16, "file": 1, "line": 17, "parent": 77}, {"command": 15, "file": 1, "line": 16, "parent": 120}, {"command": 15, "file": 1, "line": 16, "parent": 120}, {"command": 15, "file": 1, "line": 16, "parent": 120}, {"command": 15, "file": 1, "line": 16, "parent": 120}, {"command": 15, "file": 1, "line": 16, "parent": 120}, {"command": 15, "file": 1, "line": 16, "parent": 120}, {"command": 15, "file": 1, "line": 16, "parent": 120}, {"command": 15, "file": 1, "line": 16, "parent": 120}, {"command": 15, "file": 1, "line": 16, "parent": 120}, {"command": 15, "file": 1, "line": 16, "parent": 120}, {"command": 15, "file": 1, "line": 16, "parent": 120}, {"command": 15, "file": 1, "line": 16, "parent": 120}, {"command": 15, "file": 1, "line": 16, "parent": 120}, {"command": 15, "file": 1, "line": 16, "parent": 120}, {"command": 15, "file": 1, "line": 16, "parent": 120}, {"command": 15, "file": 1, "line": 16, "parent": 77}, {"command": 16, "file": 1, "line": 17, "parent": 77}, {"command": 15, "file": 1, "line": 16, "parent": 137}, {"command": 15, "file": 1, "line": 16, "parent": 137}, {"command": 15, "file": 1, "line": 16, "parent": 137}, {"command": 15, "file": 1, "line": 16, "parent": 137}, {"command": 15, "file": 1, "line": 16, "parent": 137}, {"command": 15, "file": 1, "line": 16, "parent": 137}, {"command": 15, "file": 1, "line": 16, "parent": 137}, {"command": 15, "file": 1, "line": 16, "parent": 137}, {"command": 15, "file": 1, "line": 16, "parent": 137}, {"command": 15, "file": 1, "line": 16, "parent": 137}, {"command": 15, "file": 1, "line": 16, "parent": 137}, {"command": 15, "file": 1, "line": 16, "parent": 137}, {"command": 15, "file": 1, "line": 16, "parent": 77}, {"command": 16, "file": 1, "line": 17, "parent": 77}, {"command": 15, "file": 1, "line": 16, "parent": 151}, {"command": 16, "file": 1, "line": 17, "parent": 151}, {"command": 15, "file": 1, "line": 16, "parent": 153}, {"command": 15, "file": 1, "line": 16, "parent": 153}, {"command": 15, "file": 1, "line": 16, "parent": 151}, {"command": 16, "file": 1, "line": 17, "parent": 151}, {"command": 15, "file": 1, "line": 16, "parent": 157}, {"command": 15, "file": 1, "line": 16, "parent": 157}, {"command": 15, "file": 1, "line": 16, "parent": 77}, {"command": 16, "file": 1, "line": 17, "parent": 77}, {"command": 15, "file": 1, "line": 16, "parent": 161}, {"command": 15, "file": 1, "line": 16, "parent": 77}, {"command": 16, "file": 1, "line": 17, "parent": 77}, {"command": 15, "file": 1, "line": 16, "parent": 164}, {"command": 15, "file": 1, "line": 16, "parent": 77}, {"command": 16, "file": 1, "line": 17, "parent": 77}, {"command": 15, "file": 1, "line": 16, "parent": 167}, {"command": 15, "file": 1, "line": 16, "parent": 167}, {"command": 15, "file": 1, "line": 16, "parent": 167}, {"command": 15, "file": 1, "line": 16, "parent": 167}, {"command": 15, "file": 1, "line": 16, "parent": 167}, {"command": 15, "file": 1, "line": 16, "parent": 167}, {"command": 15, "file": 1, "line": 16, "parent": 167}, {"command": 15, "file": 1, "line": 16, "parent": 167}, {"command": 15, "file": 1, "line": 16, "parent": 167}, {"command": 15, "file": 1, "line": 16, "parent": 53}, {"command": 16, "file": 1, "line": 17, "parent": 53}, {"command": 15, "file": 1, "line": 16, "parent": 178}, {"command": 16, "file": 1, "line": 17, "parent": 178}, {"command": 15, "file": 1, "line": 16, "parent": 180}, {"command": 15, "file": 1, "line": 16, "parent": 180}, {"command": 15, "file": 1, "line": 16, "parent": 53}, {"command": 16, "file": 1, "line": 17, "parent": 53}, {"command": 15, "file": 1, "line": 16, "parent": 184}, {"command": 16, "file": 1, "line": 17, "parent": 184}, {"command": 15, "file": 1, "line": 16, "parent": 186}, {"command": 15, "file": 1, "line": 16, "parent": 186}, {"command": 15, "file": 1, "line": 16, "parent": 184}, {"command": 16, "file": 1, "line": 17, "parent": 184}, {"command": 15, "file": 1, "line": 16, "parent": 190}, {"command": 15, "file": 1, "line": 16, "parent": 190}, {"command": 15, "file": 1, "line": 16, "parent": 190}, {"command": 15, "file": 1, "line": 16, "parent": 190}, {"command": 15, "file": 1, "line": 16, "parent": 184}, {"command": 16, "file": 1, "line": 17, "parent": 184}, {"command": 15, "file": 1, "line": 16, "parent": 196}, {"command": 15, "file": 1, "line": 16, "parent": 196}, {"command": 15, "file": 1, "line": 16, "parent": 184}, {"command": 16, "file": 1, "line": 17, "parent": 184}, {"command": 15, "file": 1, "line": 16, "parent": 200}, {"command": 15, "file": 1, "line": 16, "parent": 200}, {"command": 15, "file": 1, "line": 16, "parent": 184}, {"command": 16, "file": 1, "line": 17, "parent": 184}, {"command": 15, "file": 1, "line": 16, "parent": 204}, {"command": 15, "file": 1, "line": 16, "parent": 204}, {"command": 15, "file": 1, "line": 16, "parent": 184}, {"command": 16, "file": 1, "line": 17, "parent": 184}, {"command": 15, "file": 1, "line": 16, "parent": 208}, {"command": 15, "file": 1, "line": 16, "parent": 208}, {"command": 15, "file": 1, "line": 16, "parent": 208}, {"command": 15, "file": 1, "line": 16, "parent": 208}, {"command": 15, "file": 1, "line": 16, "parent": 208}, {"command": 15, "file": 1, "line": 16, "parent": 208}, {"command": 15, "file": 1, "line": 16, "parent": 184}, {"command": 16, "file": 1, "line": 17, "parent": 184}, {"command": 15, "file": 1, "line": 16, "parent": 216}, {"command": 15, "file": 1, "line": 16, "parent": 216}, {"command": 15, "file": 1, "line": 16, "parent": 53}, {"command": 16, "file": 1, "line": 17, "parent": 53}, {"command": 15, "file": 1, "line": 16, "parent": 220}, {"command": 15, "file": 1, "line": 16, "parent": 220}, {"command": 15, "file": 1, "line": 16, "parent": 53}, {"command": 16, "file": 1, "line": 17, "parent": 53}, {"command": 15, "file": 1, "line": 16, "parent": 224}, {"command": 16, "file": 1, "line": 17, "parent": 224}, {"command": 15, "file": 1, "line": 16, "parent": 226}, {"command": 15, "file": 1, "line": 16, "parent": 224}, {"command": 16, "file": 1, "line": 17, "parent": 224}, {"command": 15, "file": 1, "line": 16, "parent": 229}, {"command": 15, "file": 1, "line": 16, "parent": 229}, {"command": 15, "file": 1, "line": 16, "parent": 229}, {"command": 15, "file": 1, "line": 16, "parent": 229}, {"command": 15, "file": 1, "line": 16, "parent": 229}, {"command": 15, "file": 1, "line": 16, "parent": 224}, {"command": 16, "file": 1, "line": 17, "parent": 224}, {"command": 15, "file": 1, "line": 16, "parent": 236}, {"command": 15, "file": 1, "line": 16, "parent": 236}, {"command": 15, "file": 1, "line": 16, "parent": 236}, {"command": 15, "file": 1, "line": 16, "parent": 236}, {"command": 15, "file": 1, "line": 16, "parent": 224}, {"command": 16, "file": 1, "line": 17, "parent": 224}, {"command": 15, "file": 1, "line": 16, "parent": 242}, {"command": 15, "file": 1, "line": 16, "parent": 242}, {"command": 15, "file": 1, "line": 16, "parent": 242}, {"command": 15, "file": 1, "line": 16, "parent": 242}, {"command": 15, "file": 1, "line": 16, "parent": 242}, {"command": 15, "file": 1, "line": 16, "parent": 242}, {"command": 15, "file": 1, "line": 16, "parent": 242}, {"command": 15, "file": 1, "line": 16, "parent": 242}, {"command": 15, "file": 1, "line": 16, "parent": 242}, {"command": 15, "file": 1, "line": 16, "parent": 224}, {"command": 16, "file": 1, "line": 17, "parent": 224}, {"command": 15, "file": 1, "line": 16, "parent": 253}, {"command": 15, "file": 1, "line": 16, "parent": 253}, {"command": 15, "file": 1, "line": 16, "parent": 253}, {"command": 15, "file": 1, "line": 16, "parent": 224}, {"command": 16, "file": 1, "line": 17, "parent": 224}, {"command": 15, "file": 1, "line": 16, "parent": 258}, {"command": 15, "file": 1, "line": 16, "parent": 258}, {"command": 15, "file": 1, "line": 16, "parent": 258}, {"command": 15, "file": 1, "line": 16, "parent": 258}, {"command": 15, "file": 1, "line": 16, "parent": 258}, {"command": 15, "file": 1, "line": 16, "parent": 224}, {"command": 16, "file": 1, "line": 17, "parent": 224}, {"command": 15, "file": 1, "line": 16, "parent": 265}, {"command": 15, "file": 1, "line": 16, "parent": 265}, {"command": 15, "file": 1, "line": 16, "parent": 265}, {"command": 15, "file": 1, "line": 16, "parent": 265}, {"command": 15, "file": 1, "line": 16, "parent": 265}, {"command": 15, "file": 1, "line": 16, "parent": 265}, {"command": 15, "file": 1, "line": 16, "parent": 265}, {"command": 15, "file": 1, "line": 16, "parent": 265}, {"command": 15, "file": 1, "line": 16, "parent": 265}, {"command": 15, "file": 1, "line": 16, "parent": 265}, {"command": 15, "file": 1, "line": 16, "parent": 265}, {"command": 15, "file": 1, "line": 16, "parent": 265}, {"command": 15, "file": 1, "line": 16, "parent": 265}, {"command": 15, "file": 1, "line": 16, "parent": 224}, {"command": 16, "file": 1, "line": 17, "parent": 224}, {"command": 15, "file": 1, "line": 16, "parent": 280}, {"command": 15, "file": 1, "line": 16, "parent": 280}, {"command": 15, "file": 1, "line": 16, "parent": 280}, {"command": 15, "file": 1, "line": 16, "parent": 280}, {"command": 15, "file": 1, "line": 16, "parent": 280}, {"command": 15, "file": 1, "line": 16, "parent": 224}, {"command": 16, "file": 1, "line": 17, "parent": 224}, {"command": 15, "file": 1, "line": 16, "parent": 287}, {"command": 16, "file": 1, "line": 17, "parent": 287}, {"command": 15, "file": 1, "line": 16, "parent": 289}, {"command": 15, "file": 1, "line": 16, "parent": 289}, {"command": 15, "file": 1, "line": 16, "parent": 289}, {"command": 15, "file": 1, "line": 16, "parent": 287}, {"command": 16, "file": 1, "line": 17, "parent": 287}, {"command": 15, "file": 1, "line": 16, "parent": 294}, {"command": 15, "file": 1, "line": 16, "parent": 294}, {"command": 15, "file": 1, "line": 16, "parent": 294}, {"command": 15, "file": 1, "line": 16, "parent": 294}, {"command": 15, "file": 1, "line": 16, "parent": 294}, {"command": 15, "file": 1, "line": 16, "parent": 294}, {"command": 15, "file": 1, "line": 16, "parent": 53}, {"command": 16, "file": 1, "line": 17, "parent": 53}, {"command": 15, "file": 1, "line": 16, "parent": 302}, {"command": 16, "file": 1, "line": 17, "parent": 302}, {"command": 15, "file": 1, "line": 16, "parent": 304}, {"command": 15, "file": 1, "line": 16, "parent": 302}, {"command": 15, "file": 1, "line": 16, "parent": 302}, {"command": 15, "file": 1, "line": 16, "parent": 302}, {"command": 16, "file": 1, "line": 17, "parent": 302}, {"command": 15, "file": 1, "line": 16, "parent": 309}, {"command": 15, "file": 1, "line": 16, "parent": 53}, {"command": 16, "file": 1, "line": 17, "parent": 53}, {"command": 15, "file": 1, "line": 16, "parent": 312}, {"command": 15, "file": 1, "line": 16, "parent": 312}, {"command": 16, "file": 1, "line": 17, "parent": 312}, {"command": 15, "file": 1, "line": 16, "parent": 315}, {"command": 15, "file": 1, "line": 16, "parent": 315}, {"command": 15, "file": 1, "line": 16, "parent": 53}, {"command": 16, "file": 1, "line": 17, "parent": 53}, {"command": 15, "file": 1, "line": 16, "parent": 319}, {"command": 15, "file": 1, "line": 16, "parent": 319}, {"command": 16, "file": 1, "line": 17, "parent": 319}, {"command": 15, "file": 1, "line": 16, "parent": 322}, {"command": 15, "file": 1, "line": 16, "parent": 322}, {"command": 15, "file": 1, "line": 16, "parent": 319}, {"command": 16, "file": 1, "line": 17, "parent": 319}, {"command": 15, "file": 1, "line": 16, "parent": 326}, {"command": 15, "file": 1, "line": 16, "parent": 326}, {"command": 15, "file": 1, "line": 16, "parent": 326}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd"}, {"backtrace": 24, "fragment": "-Zc:__cplusplus"}, {"backtrace": 24, "fragment": "-permissive-"}, {"backtrace": 24, "fragment": "-utf-8"}], "defines": [{"backtrace": 52, "define": "APP_VERSION=\"1.1.13\""}, {"backtrace": 6, "define": "QT_CHARTS_LIB"}, {"backtrace": 24, "define": "QT_CORE_LIB"}, {"backtrace": 6, "define": "QT_GUI_LIB"}, {"backtrace": 6, "define": "QT_NETWORK_LIB"}, {"backtrace": 6, "define": "QT_OPENGLWIDGETS_LIB"}, {"backtrace": 6, "define": "QT_OPENGL_LIB"}, {"backtrace": 6, "define": "QT_SVG_LIB"}, {"backtrace": 6, "define": "QT_WIDGETS_LIB"}, {"backtrace": 24, "define": "UNICODE"}, {"backtrace": 24, "define": "WIN32"}, {"backtrace": 24, "define": "WIN64"}, {"backtrace": 24, "define": "_ENABLE_EXTENDED_ALIGNED_STORAGE"}, {"backtrace": 24, "define": "_UNICODE"}, {"backtrace": 24, "define": "_WIN64"}], "includes": [{"backtrace": 0, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/TPCC_autogen/include"}, {"backtrace": 54, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction"}, {"backtrace": 56, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/AppSettings"}, {"backtrace": 57, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/AutoStartManager"}, {"backtrace": 58, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/CTL"}, {"backtrace": 60, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/CTL/BlockingQueue"}, {"backtrace": 61, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/CTL/Singleton"}, {"backtrace": 62, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/DebugManager"}, {"backtrace": 63, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/EqualizerTool"}, {"backtrace": 64, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/GlobalFont"}, {"backtrace": 65, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/SingleInstanceManager"}, {"backtrace": 66, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/Solo"}, {"backtrace": 67, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/TrialManager"}, {"backtrace": 68, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/USBAudioManager"}, {"backtrace": 69, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/Updater"}, {"backtrace": 71, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/Updater/UpdaterBase"}, {"backtrace": 72, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/Updater/UpdaterFactory"}, {"backtrace": 73, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/Updater/UpdaterFirmwareM1"}, {"backtrace": 74, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/Updater/UpdaterSoftware"}, {"backtrace": 75, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/Workspace"}, {"backtrace": 76, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget"}, {"backtrace": 78, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Battery"}, {"backtrace": 80, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Battery/BatteryS1M1"}, {"backtrace": 81, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/ButtonBox"}, {"backtrace": 83, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/ButtonBox/ButtonBoxS1M1"}, {"backtrace": 84, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Chart"}, {"backtrace": 85, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Circle"}, {"backtrace": 87, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Circle/CircleS1M1"}, {"backtrace": 88, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/ComboBox"}, {"backtrace": 90, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/ComboBox/ComboBoxS1M1"}, {"backtrace": 91, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/ComboBox/ComboBoxS1M2"}, {"backtrace": 92, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/ComboBox/ComboBoxS1M3"}, {"backtrace": 93, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Dial"}, {"backtrace": 95, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M1"}, {"backtrace": 96, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M2"}, {"backtrace": 97, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M3"}, {"backtrace": 98, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M4"}, {"backtrace": 99, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M5"}, {"backtrace": 100, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M6"}, {"backtrace": 101, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/EqualizerController"}, {"backtrace": 103, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/EqualizerController/EqualizerControllerS1M1"}, {"backtrace": 104, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/FramelessWindow"}, {"backtrace": 105, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Menu"}, {"backtrace": 107, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Menu/MenuS1M1"}, {"backtrace": 108, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox"}, {"backtrace": 110, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxS1M1"}, {"backtrace": 111, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxS2M1"}, {"backtrace": 112, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxS3M1"}, {"backtrace": 113, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxWidget"}, {"backtrace": 115, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget1"}, {"backtrace": 116, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget2"}, {"backtrace": 117, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget3"}, {"backtrace": 118, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget4"}, {"backtrace": 119, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton"}, {"backtrace": 121, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M1"}, {"backtrace": 122, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M10"}, {"backtrace": 123, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M11"}, {"backtrace": 124, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M12"}, {"backtrace": 125, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M13"}, {"backtrace": 126, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M14"}, {"backtrace": 127, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M15"}, {"backtrace": 128, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M2"}, {"backtrace": 129, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M3"}, {"backtrace": 130, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M4"}, {"backtrace": 131, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M5"}, {"backtrace": 132, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M6"}, {"backtrace": 133, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M7"}, {"backtrace": 134, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M8"}, {"backtrace": 135, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M9"}, {"backtrace": 136, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup"}, {"backtrace": 138, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M1"}, {"backtrace": 139, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M10"}, {"backtrace": 140, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M11"}, {"backtrace": 141, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M12"}, {"backtrace": 142, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M2"}, {"backtrace": 143, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M3"}, {"backtrace": 144, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M4"}, {"backtrace": 145, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M5"}, {"backtrace": 146, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M6"}, {"backtrace": 147, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M7"}, {"backtrace": 148, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M8"}, {"backtrace": 149, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M9"}, {"backtrace": 150, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Slider"}, {"backtrace": 152, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/HSlider"}, {"backtrace": 154, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/HSlider/HSliderS1M1"}, {"backtrace": 155, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/HSlider/HSliderS2M1"}, {"backtrace": 156, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/VSlider"}, {"backtrace": 158, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/VSlider/VSliderS1M1"}, {"backtrace": 159, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/VSlider/VSliderS1M2"}, {"backtrace": 160, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/TabWidget"}, {"backtrace": 162, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/TabWidget/TabWidgetS1M1"}, {"backtrace": 163, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/ToolButton"}, {"backtrace": 165, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/ToolButton/ToolButtonS1M1"}, {"backtrace": 166, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter"}, {"backtrace": 168, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M1"}, {"backtrace": 169, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M2"}, {"backtrace": 170, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M3"}, {"backtrace": 171, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M4"}, {"backtrace": 172, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M5"}, {"backtrace": 173, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M6"}, {"backtrace": 174, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M7"}, {"backtrace": 175, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M8"}, {"backtrace": 176, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS2M1"}, {"backtrace": 177, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceConnector"}, {"backtrace": 179, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceConnector/DeviceConnectorView"}, {"backtrace": 181, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceConnector/DeviceConnectorView/DeviceConnectorViewBase"}, {"backtrace": 182, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceConnector/DeviceConnectorView/DeviceConnectorViewS1M1"}, {"backtrace": 183, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField"}, {"backtrace": 185, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldEffect"}, {"backtrace": 187, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldEffect/FieldEffectBase1"}, {"backtrace": 188, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldEffect/FieldEffectS1M1"}, {"backtrace": 189, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldHead"}, {"backtrace": 191, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldHead/FieldHeadBase1"}, {"backtrace": 192, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldHead/FieldHeadBase2"}, {"backtrace": 193, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldHead/FieldHeadS1M1"}, {"backtrace": 194, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldHead/FieldHeadS1M2"}, {"backtrace": 195, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldInput"}, {"backtrace": 197, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldInput/FieldInputBase1"}, {"backtrace": 198, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldInput/FieldInputS1M1"}, {"backtrace": 199, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldLoopback"}, {"backtrace": 201, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldLoopback/FieldLoopbackBase1"}, {"backtrace": 202, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldLoopback/FieldLoopbackS1M1"}, {"backtrace": 203, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldMixer"}, {"backtrace": 205, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldMixer/FieldMixerBase1"}, {"backtrace": 206, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldMixer/FieldMixerS1M1"}, {"backtrace": 207, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin"}, {"backtrace": 209, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin/FieldOriginBase1"}, {"backtrace": 210, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin/FieldOriginBase2"}, {"backtrace": 211, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin/FieldOriginBase3"}, {"backtrace": 212, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin/FieldOriginS1M1"}, {"backtrace": 213, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin/FieldOriginS1M2"}, {"backtrace": 214, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin/FieldOriginS2M1"}, {"backtrace": 215, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldOutput"}, {"backtrace": 217, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldOutput/FieldOutputBase1"}, {"backtrace": 218, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldOutput/FieldOutputS1M1"}, {"backtrace": 219, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceMainWindow"}, {"backtrace": 221, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceMainWindow/MainWindow_Base"}, {"backtrace": 222, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceMainWindow/MainWindow_M62"}, {"backtrace": 223, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget"}, {"backtrace": 225, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetAutoGain"}, {"backtrace": 227, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetAutoGain/AutoGainS1M1"}, {"backtrace": 228, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEffect"}, {"backtrace": 230, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEffect/EffectBase"}, {"backtrace": 231, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEffect/EffectS1M1"}, {"backtrace": 232, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEffect/EffectS1M2"}, {"backtrace": 233, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEffect/EffectS1M3"}, {"backtrace": 234, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEffect/EffectS1M4"}, {"backtrace": 235, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEqualizer"}, {"backtrace": 237, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEqualizer/EqualizerBase"}, {"backtrace": 238, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEqualizer/EqualizerPanelS1M1"}, {"backtrace": 239, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEqualizer/EqualizerS1M1"}, {"backtrace": 240, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEqualizer/EqualizerS1M2"}, {"backtrace": 241, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput"}, {"backtrace": 243, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputBase"}, {"backtrace": 244, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M1"}, {"backtrace": 245, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M2"}, {"backtrace": 246, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M3"}, {"backtrace": 247, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M4"}, {"backtrace": 248, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M5"}, {"backtrace": 249, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M6"}, {"backtrace": 250, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS2M1"}, {"backtrace": 251, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS2M2"}, {"backtrace": 252, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetLoopback"}, {"backtrace": 254, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetLoopback/LoopbackBase"}, {"backtrace": 255, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetLoopback/LoopbackS1M1"}, {"backtrace": 256, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetLoopback/LoopbackS1M2"}, {"backtrace": 257, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer"}, {"backtrace": 259, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer/MixerBase"}, {"backtrace": 260, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer/MixerS1M1"}, {"backtrace": 261, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer/MixerS1M2"}, {"backtrace": 262, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer/MixerS1M3"}, {"backtrace": 263, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer/MixerS1M4"}, {"backtrace": 264, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin"}, {"backtrace": 266, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginBase"}, {"backtrace": 267, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M1"}, {"backtrace": 268, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M10"}, {"backtrace": 269, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M11"}, {"backtrace": 270, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M12"}, {"backtrace": 271, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M13"}, {"backtrace": 272, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M2"}, {"backtrace": 273, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M3"}, {"backtrace": 274, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M4"}, {"backtrace": 275, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M6"}, {"backtrace": 276, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M7"}, {"backtrace": 277, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M8"}, {"backtrace": 278, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M9"}, {"backtrace": 279, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOutput"}, {"backtrace": 281, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOutput/OutputBase"}, {"backtrace": 282, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOutput/OutputS1M1"}, {"backtrace": 283, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOutput/OutputS1M2"}, {"backtrace": 284, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOutput/OutputS1M3"}, {"backtrace": 285, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOutput/OutputS1M4"}, {"backtrace": 286, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate"}, {"backtrace": 288, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/DeviceAll"}, {"backtrace": 290, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/DeviceAll/WidgetAbout1"}, {"backtrace": 291, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/DeviceAll/WidgetAudio1"}, {"backtrace": 292, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/DeviceAll/WidgetSystem1"}, {"backtrace": 293, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/M62"}, {"backtrace": 295, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget1"}, {"backtrace": 296, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget2"}, {"backtrace": 297, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget3"}, {"backtrace": 298, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget5"}, {"backtrace": 299, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget6"}, {"backtrace": 300, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget7"}, {"backtrace": 301, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/ThirdPartyResource"}, {"backtrace": 303, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/ThirdPartyResource/Component"}, {"backtrace": 305, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/ThirdPartyResource/Component/TKSpline"}, {"backtrace": 306, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/ThirdPartyResource/Font"}, {"backtrace": 307, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/ThirdPartyResource/Icon"}, {"backtrace": 308, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/ThirdPartyResource/Image"}, {"backtrace": 310, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/ThirdPartyResource/Image/PushButtonGroup"}, {"backtrace": 311, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/USBAudio"}, {"backtrace": 313, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/USBAudio/API"}, {"backtrace": 314, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/USBAudio/SDK"}, {"backtrace": 316, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/USBAudio/SDK/MAC"}, {"backtrace": 317, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/USBAudio/SDK/WIN"}, {"backtrace": 318, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/USBHID"}, {"backtrace": 320, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/USBHID/API"}, {"backtrace": 321, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/USBHID/Device"}, {"backtrace": 323, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/USBHID/Device/DeviceBase"}, {"backtrace": 324, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/USBHID/Device/DeviceM62"}, {"backtrace": 325, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/USBHID/SDK"}, {"backtrace": 327, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/USBHID/SDK/linux"}, {"backtrace": 328, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/USBHID/SDK/mac"}, {"backtrace": 329, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/USBHID/SDK/win"}, {"backtrace": 330, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/3d59d5"}, {"backtrace": 331, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/d32d81"}, {"backtrace": 332, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/9932bd"}, {"backtrace": 333, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/8dec82"}, {"backtrace": 334, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/1ae6f3"}, {"backtrace": 335, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/3e117d"}, {"backtrace": 336, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/409ced"}, {"backtrace": 337, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/dd3f7a"}, {"backtrace": 338, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/0d6c37"}, {"backtrace": 339, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/9f94b4"}, {"backtrace": 340, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/3ce7e0"}, {"backtrace": 341, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/325c6f"}, {"backtrace": 342, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/8b1eee"}, {"backtrace": 343, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/b433c2"}, {"backtrace": 344, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/97c906"}, {"backtrace": 345, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/973edf"}, {"backtrace": 346, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/b1469a"}, {"backtrace": 347, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/8dbe06"}, {"backtrace": 348, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/c7545b"}, {"backtrace": 349, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/51133f"}, {"backtrace": 350, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/8e3cee"}, {"backtrace": 351, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/fb6557"}, {"backtrace": 352, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/77bb6e"}, {"backtrace": 353, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/3463eb"}, {"backtrace": 354, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/88c8aa"}, {"backtrace": 355, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/38583a"}, {"backtrace": 356, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/951c6c"}, {"backtrace": 357, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/9e24b0"}, {"backtrace": 358, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/f74d14"}, {"backtrace": 359, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/6bb67c"}, {"backtrace": 360, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/e2bf37"}, {"backtrace": 361, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/47bd68"}, {"backtrace": 362, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/3b9917"}, {"backtrace": 363, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/811480"}, {"backtrace": 364, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/ec314e"}, {"backtrace": 365, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/6293d7"}, {"backtrace": 366, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/cf1354"}, {"backtrace": 367, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/3a1aa4"}, {"backtrace": 368, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/825ead"}, {"backtrace": 369, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/77fc9c"}, {"backtrace": 370, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/c58c53"}, {"backtrace": 371, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/45f5d9"}, {"backtrace": 372, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/873115"}, {"backtrace": 373, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/1ad9f8"}, {"backtrace": 374, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/d11dc0"}, {"backtrace": 375, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/e42e24"}, {"backtrace": 376, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/d0d34e"}, {"backtrace": 377, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/dc5571"}, {"backtrace": 378, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/0a4477"}, {"backtrace": 379, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/7dbb21"}, {"backtrace": 380, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/cdde82"}, {"backtrace": 381, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/23a48c"}, {"backtrace": 382, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/aa53b8"}, {"backtrace": 383, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/94bb1c"}, {"backtrace": 384, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/246d94"}, {"backtrace": 385, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/cac869"}, {"backtrace": 386, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/ab8e07"}, {"backtrace": 387, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/2fb990"}, {"backtrace": 388, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/0da7eb"}, {"backtrace": 389, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/c40ae0"}, {"backtrace": 390, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/f6e8f4"}, {"backtrace": 391, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/9ddbde"}, {"backtrace": 24, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtCore"}, {"backtrace": 24, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include"}, {"backtrace": 24, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/mkspecs/win32-msvc"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtWidgets"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtGui"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtNetwork"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtCharts"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtOpenGL"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtOpenGLWidgets"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtSvg"}], "language": "CXX", "languageStandard": {"backtraces": [24], "standard": "17"}, "sourceIndexes": [0, 1, 6, 8, 10, 13, 15, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 65, 68, 71, 74, 76, 79, 82, 85, 87, 90, 93, 96, 99, 102, 105, 108, 111, 113, 116, 119, 121, 124, 127, 130, 133, 135, 138, 141, 144, 147, 150, 153, 155, 158, 161, 164, 167, 170, 172, 175, 178, 181, 184, 187, 190, 193, 194, 199, 201, 204, 206, 208, 211, 212, 215, 218, 220, 222, 224, 226, 228, 230, 232, 234, 236, 238, 240, 241, 245, 247, 249, 251, 253, 255, 258, 261, 264, 267, 269, 271, 273, 275, 277, 279, 281, 283, 285, 287, 289, 291, 293, 295, 297, 300, 303, 306, 309, 312, 315, 318, 321, 324, 327, 330, 333, 335, 337, 339, 341, 343, 345, 347, 349, 351, 353, 355, 357, 359, 361, 366, 368, 372, 374, 376, 378, 380, 382, 384, 386, 389, 391, 393, 395, 396, 399, 401, 412, 413, 414, 415, 416, 417, 418, 419, 420, 421, 422, 423, 424, 425, 426, 427, 428, 429, 430, 431, 432, 486, 551]}, {"compileCommandFragments": [{"fragment": "-DWIN32 -D_DEBUG"}], "defines": [{"backtrace": 52, "define": "APP_VERSION=\"1.1.13\""}, {"backtrace": 6, "define": "QT_CHARTS_LIB"}, {"backtrace": 24, "define": "QT_CORE_LIB"}, {"backtrace": 6, "define": "QT_GUI_LIB"}, {"backtrace": 6, "define": "QT_NETWORK_LIB"}, {"backtrace": 6, "define": "QT_OPENGLWIDGETS_LIB"}, {"backtrace": 6, "define": "QT_OPENGL_LIB"}, {"backtrace": 6, "define": "QT_SVG_LIB"}, {"backtrace": 6, "define": "QT_WIDGETS_LIB"}, {"backtrace": 24, "define": "UNICODE"}, {"backtrace": 24, "define": "WIN32"}, {"backtrace": 24, "define": "WIN64"}, {"backtrace": 24, "define": "_ENABLE_EXTENDED_ALIGNED_STORAGE"}, {"backtrace": 24, "define": "_UNICODE"}, {"backtrace": 24, "define": "_WIN64"}], "includes": [{"backtrace": 0, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/TPCC_autogen/include"}, {"backtrace": 54, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction"}, {"backtrace": 56, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/AppSettings"}, {"backtrace": 57, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/AutoStartManager"}, {"backtrace": 58, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/CTL"}, {"backtrace": 60, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/CTL/BlockingQueue"}, {"backtrace": 61, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/CTL/Singleton"}, {"backtrace": 62, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/DebugManager"}, {"backtrace": 63, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/EqualizerTool"}, {"backtrace": 64, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/GlobalFont"}, {"backtrace": 65, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/SingleInstanceManager"}, {"backtrace": 66, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/Solo"}, {"backtrace": 67, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/TrialManager"}, {"backtrace": 68, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/USBAudioManager"}, {"backtrace": 69, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/Updater"}, {"backtrace": 71, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/Updater/UpdaterBase"}, {"backtrace": 72, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/Updater/UpdaterFactory"}, {"backtrace": 73, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/Updater/UpdaterFirmwareM1"}, {"backtrace": 74, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/Updater/UpdaterSoftware"}, {"backtrace": 75, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/Workspace"}, {"backtrace": 76, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget"}, {"backtrace": 78, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Battery"}, {"backtrace": 80, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Battery/BatteryS1M1"}, {"backtrace": 81, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/ButtonBox"}, {"backtrace": 83, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/ButtonBox/ButtonBoxS1M1"}, {"backtrace": 84, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Chart"}, {"backtrace": 85, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Circle"}, {"backtrace": 87, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Circle/CircleS1M1"}, {"backtrace": 88, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/ComboBox"}, {"backtrace": 90, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/ComboBox/ComboBoxS1M1"}, {"backtrace": 91, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/ComboBox/ComboBoxS1M2"}, {"backtrace": 92, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/ComboBox/ComboBoxS1M3"}, {"backtrace": 93, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Dial"}, {"backtrace": 95, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M1"}, {"backtrace": 96, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M2"}, {"backtrace": 97, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M3"}, {"backtrace": 98, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M4"}, {"backtrace": 99, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M5"}, {"backtrace": 100, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M6"}, {"backtrace": 101, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/EqualizerController"}, {"backtrace": 103, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/EqualizerController/EqualizerControllerS1M1"}, {"backtrace": 104, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/FramelessWindow"}, {"backtrace": 105, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Menu"}, {"backtrace": 107, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Menu/MenuS1M1"}, {"backtrace": 108, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox"}, {"backtrace": 110, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxS1M1"}, {"backtrace": 111, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxS2M1"}, {"backtrace": 112, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxS3M1"}, {"backtrace": 113, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxWidget"}, {"backtrace": 115, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget1"}, {"backtrace": 116, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget2"}, {"backtrace": 117, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget3"}, {"backtrace": 118, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget4"}, {"backtrace": 119, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton"}, {"backtrace": 121, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M1"}, {"backtrace": 122, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M10"}, {"backtrace": 123, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M11"}, {"backtrace": 124, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M12"}, {"backtrace": 125, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M13"}, {"backtrace": 126, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M14"}, {"backtrace": 127, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M15"}, {"backtrace": 128, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M2"}, {"backtrace": 129, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M3"}, {"backtrace": 130, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M4"}, {"backtrace": 131, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M5"}, {"backtrace": 132, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M6"}, {"backtrace": 133, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M7"}, {"backtrace": 134, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M8"}, {"backtrace": 135, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M9"}, {"backtrace": 136, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup"}, {"backtrace": 138, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M1"}, {"backtrace": 139, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M10"}, {"backtrace": 140, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M11"}, {"backtrace": 141, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M12"}, {"backtrace": 142, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M2"}, {"backtrace": 143, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M3"}, {"backtrace": 144, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M4"}, {"backtrace": 145, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M5"}, {"backtrace": 146, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M6"}, {"backtrace": 147, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M7"}, {"backtrace": 148, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M8"}, {"backtrace": 149, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M9"}, {"backtrace": 150, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Slider"}, {"backtrace": 152, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/HSlider"}, {"backtrace": 154, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/HSlider/HSliderS1M1"}, {"backtrace": 155, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/HSlider/HSliderS2M1"}, {"backtrace": 156, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/VSlider"}, {"backtrace": 158, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/VSlider/VSliderS1M1"}, {"backtrace": 159, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/VSlider/VSliderS1M2"}, {"backtrace": 160, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/TabWidget"}, {"backtrace": 162, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/TabWidget/TabWidgetS1M1"}, {"backtrace": 163, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/ToolButton"}, {"backtrace": 165, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/ToolButton/ToolButtonS1M1"}, {"backtrace": 166, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter"}, {"backtrace": 168, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M1"}, {"backtrace": 169, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M2"}, {"backtrace": 170, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M3"}, {"backtrace": 171, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M4"}, {"backtrace": 172, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M5"}, {"backtrace": 173, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M6"}, {"backtrace": 174, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M7"}, {"backtrace": 175, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M8"}, {"backtrace": 176, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS2M1"}, {"backtrace": 177, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceConnector"}, {"backtrace": 179, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceConnector/DeviceConnectorView"}, {"backtrace": 181, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceConnector/DeviceConnectorView/DeviceConnectorViewBase"}, {"backtrace": 182, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceConnector/DeviceConnectorView/DeviceConnectorViewS1M1"}, {"backtrace": 183, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField"}, {"backtrace": 185, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldEffect"}, {"backtrace": 187, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldEffect/FieldEffectBase1"}, {"backtrace": 188, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldEffect/FieldEffectS1M1"}, {"backtrace": 189, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldHead"}, {"backtrace": 191, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldHead/FieldHeadBase1"}, {"backtrace": 192, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldHead/FieldHeadBase2"}, {"backtrace": 193, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldHead/FieldHeadS1M1"}, {"backtrace": 194, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldHead/FieldHeadS1M2"}, {"backtrace": 195, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldInput"}, {"backtrace": 197, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldInput/FieldInputBase1"}, {"backtrace": 198, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldInput/FieldInputS1M1"}, {"backtrace": 199, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldLoopback"}, {"backtrace": 201, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldLoopback/FieldLoopbackBase1"}, {"backtrace": 202, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldLoopback/FieldLoopbackS1M1"}, {"backtrace": 203, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldMixer"}, {"backtrace": 205, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldMixer/FieldMixerBase1"}, {"backtrace": 206, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldMixer/FieldMixerS1M1"}, {"backtrace": 207, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin"}, {"backtrace": 209, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin/FieldOriginBase1"}, {"backtrace": 210, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin/FieldOriginBase2"}, {"backtrace": 211, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin/FieldOriginBase3"}, {"backtrace": 212, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin/FieldOriginS1M1"}, {"backtrace": 213, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin/FieldOriginS1M2"}, {"backtrace": 214, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin/FieldOriginS2M1"}, {"backtrace": 215, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldOutput"}, {"backtrace": 217, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldOutput/FieldOutputBase1"}, {"backtrace": 218, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldOutput/FieldOutputS1M1"}, {"backtrace": 219, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceMainWindow"}, {"backtrace": 221, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceMainWindow/MainWindow_Base"}, {"backtrace": 222, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceMainWindow/MainWindow_M62"}, {"backtrace": 223, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget"}, {"backtrace": 225, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetAutoGain"}, {"backtrace": 227, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetAutoGain/AutoGainS1M1"}, {"backtrace": 228, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEffect"}, {"backtrace": 230, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEffect/EffectBase"}, {"backtrace": 231, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEffect/EffectS1M1"}, {"backtrace": 232, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEffect/EffectS1M2"}, {"backtrace": 233, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEffect/EffectS1M3"}, {"backtrace": 234, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEffect/EffectS1M4"}, {"backtrace": 235, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEqualizer"}, {"backtrace": 237, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEqualizer/EqualizerBase"}, {"backtrace": 238, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEqualizer/EqualizerPanelS1M1"}, {"backtrace": 239, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEqualizer/EqualizerS1M1"}, {"backtrace": 240, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEqualizer/EqualizerS1M2"}, {"backtrace": 241, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput"}, {"backtrace": 243, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputBase"}, {"backtrace": 244, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M1"}, {"backtrace": 245, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M2"}, {"backtrace": 246, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M3"}, {"backtrace": 247, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M4"}, {"backtrace": 248, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M5"}, {"backtrace": 249, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M6"}, {"backtrace": 250, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS2M1"}, {"backtrace": 251, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS2M2"}, {"backtrace": 252, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetLoopback"}, {"backtrace": 254, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetLoopback/LoopbackBase"}, {"backtrace": 255, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetLoopback/LoopbackS1M1"}, {"backtrace": 256, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetLoopback/LoopbackS1M2"}, {"backtrace": 257, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer"}, {"backtrace": 259, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer/MixerBase"}, {"backtrace": 260, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer/MixerS1M1"}, {"backtrace": 261, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer/MixerS1M2"}, {"backtrace": 262, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer/MixerS1M3"}, {"backtrace": 263, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer/MixerS1M4"}, {"backtrace": 264, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin"}, {"backtrace": 266, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginBase"}, {"backtrace": 267, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M1"}, {"backtrace": 268, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M10"}, {"backtrace": 269, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M11"}, {"backtrace": 270, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M12"}, {"backtrace": 271, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M13"}, {"backtrace": 272, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M2"}, {"backtrace": 273, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M3"}, {"backtrace": 274, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M4"}, {"backtrace": 275, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M6"}, {"backtrace": 276, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M7"}, {"backtrace": 277, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M8"}, {"backtrace": 278, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M9"}, {"backtrace": 279, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOutput"}, {"backtrace": 281, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOutput/OutputBase"}, {"backtrace": 282, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOutput/OutputS1M1"}, {"backtrace": 283, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOutput/OutputS1M2"}, {"backtrace": 284, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOutput/OutputS1M3"}, {"backtrace": 285, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOutput/OutputS1M4"}, {"backtrace": 286, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate"}, {"backtrace": 288, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/DeviceAll"}, {"backtrace": 290, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/DeviceAll/WidgetAbout1"}, {"backtrace": 291, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/DeviceAll/WidgetAudio1"}, {"backtrace": 292, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/DeviceAll/WidgetSystem1"}, {"backtrace": 293, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/M62"}, {"backtrace": 295, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget1"}, {"backtrace": 296, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget2"}, {"backtrace": 297, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget3"}, {"backtrace": 298, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget5"}, {"backtrace": 299, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget6"}, {"backtrace": 300, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget7"}, {"backtrace": 301, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/ThirdPartyResource"}, {"backtrace": 303, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/ThirdPartyResource/Component"}, {"backtrace": 305, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/ThirdPartyResource/Component/TKSpline"}, {"backtrace": 306, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/ThirdPartyResource/Font"}, {"backtrace": 307, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/ThirdPartyResource/Icon"}, {"backtrace": 308, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/ThirdPartyResource/Image"}, {"backtrace": 310, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/ThirdPartyResource/Image/PushButtonGroup"}, {"backtrace": 311, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/USBAudio"}, {"backtrace": 313, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/USBAudio/API"}, {"backtrace": 314, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/USBAudio/SDK"}, {"backtrace": 316, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/USBAudio/SDK/MAC"}, {"backtrace": 317, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/USBAudio/SDK/WIN"}, {"backtrace": 318, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/USBHID"}, {"backtrace": 320, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/USBHID/API"}, {"backtrace": 321, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/USBHID/Device"}, {"backtrace": 323, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/USBHID/Device/DeviceBase"}, {"backtrace": 324, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/USBHID/Device/DeviceM62"}, {"backtrace": 325, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/USBHID/SDK"}, {"backtrace": 327, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/USBHID/SDK/linux"}, {"backtrace": 328, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/USBHID/SDK/mac"}, {"backtrace": 329, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/USBHID/SDK/win"}, {"backtrace": 330, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/3d59d5"}, {"backtrace": 331, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/d32d81"}, {"backtrace": 332, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/9932bd"}, {"backtrace": 333, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/8dec82"}, {"backtrace": 334, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/1ae6f3"}, {"backtrace": 335, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/3e117d"}, {"backtrace": 336, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/409ced"}, {"backtrace": 337, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/dd3f7a"}, {"backtrace": 338, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/0d6c37"}, {"backtrace": 339, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/9f94b4"}, {"backtrace": 340, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/3ce7e0"}, {"backtrace": 341, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/325c6f"}, {"backtrace": 342, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/8b1eee"}, {"backtrace": 343, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/b433c2"}, {"backtrace": 344, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/97c906"}, {"backtrace": 345, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/973edf"}, {"backtrace": 346, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/b1469a"}, {"backtrace": 347, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/8dbe06"}, {"backtrace": 348, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/c7545b"}, {"backtrace": 349, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/51133f"}, {"backtrace": 350, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/8e3cee"}, {"backtrace": 351, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/fb6557"}, {"backtrace": 352, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/77bb6e"}, {"backtrace": 353, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/3463eb"}, {"backtrace": 354, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/88c8aa"}, {"backtrace": 355, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/38583a"}, {"backtrace": 356, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/951c6c"}, {"backtrace": 357, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/9e24b0"}, {"backtrace": 358, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/f74d14"}, {"backtrace": 359, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/6bb67c"}, {"backtrace": 360, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/e2bf37"}, {"backtrace": 361, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/47bd68"}, {"backtrace": 362, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/3b9917"}, {"backtrace": 363, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/811480"}, {"backtrace": 364, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/ec314e"}, {"backtrace": 365, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/6293d7"}, {"backtrace": 366, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/cf1354"}, {"backtrace": 367, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/3a1aa4"}, {"backtrace": 368, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/825ead"}, {"backtrace": 369, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/77fc9c"}, {"backtrace": 370, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/c58c53"}, {"backtrace": 371, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/45f5d9"}, {"backtrace": 372, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/873115"}, {"backtrace": 373, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/1ad9f8"}, {"backtrace": 374, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/d11dc0"}, {"backtrace": 375, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/e42e24"}, {"backtrace": 376, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/d0d34e"}, {"backtrace": 377, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/dc5571"}, {"backtrace": 378, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/0a4477"}, {"backtrace": 379, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/7dbb21"}, {"backtrace": 380, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/cdde82"}, {"backtrace": 381, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/23a48c"}, {"backtrace": 382, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/aa53b8"}, {"backtrace": 383, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/94bb1c"}, {"backtrace": 384, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/246d94"}, {"backtrace": 385, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/cac869"}, {"backtrace": 386, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/ab8e07"}, {"backtrace": 387, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/2fb990"}, {"backtrace": 388, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/0da7eb"}, {"backtrace": 389, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/c40ae0"}, {"backtrace": 390, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/f6e8f4"}, {"backtrace": 391, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/9ddbde"}, {"backtrace": 24, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtCore"}, {"backtrace": 24, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include"}, {"backtrace": 24, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/mkspecs/win32-msvc"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtWidgets"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtGui"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtNetwork"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtCharts"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtOpenGL"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtOpenGLWidgets"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtSvg"}], "language": "RC", "sourceIndexes": [364]}, {"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /Zi /Ob0 /Od /RTC1 -MDd"}, {"backtrace": 24, "fragment": "-utf-8"}], "defines": [{"backtrace": 52, "define": "APP_VERSION=\"1.1.13\""}, {"backtrace": 6, "define": "QT_CHARTS_LIB"}, {"backtrace": 24, "define": "QT_CORE_LIB"}, {"backtrace": 6, "define": "QT_GUI_LIB"}, {"backtrace": 6, "define": "QT_NETWORK_LIB"}, {"backtrace": 6, "define": "QT_OPENGLWIDGETS_LIB"}, {"backtrace": 6, "define": "QT_OPENGL_LIB"}, {"backtrace": 6, "define": "QT_SVG_LIB"}, {"backtrace": 6, "define": "QT_WIDGETS_LIB"}, {"backtrace": 24, "define": "UNICODE"}, {"backtrace": 24, "define": "WIN32"}, {"backtrace": 24, "define": "WIN64"}, {"backtrace": 24, "define": "_ENABLE_EXTENDED_ALIGNED_STORAGE"}, {"backtrace": 24, "define": "_UNICODE"}, {"backtrace": 24, "define": "_WIN64"}], "includes": [{"backtrace": 0, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/TPCC_autogen/include"}, {"backtrace": 54, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction"}, {"backtrace": 56, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/AppSettings"}, {"backtrace": 57, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/AutoStartManager"}, {"backtrace": 58, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/CTL"}, {"backtrace": 60, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/CTL/BlockingQueue"}, {"backtrace": 61, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/CTL/Singleton"}, {"backtrace": 62, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/DebugManager"}, {"backtrace": 63, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/EqualizerTool"}, {"backtrace": 64, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/GlobalFont"}, {"backtrace": 65, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/SingleInstanceManager"}, {"backtrace": 66, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/Solo"}, {"backtrace": 67, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/TrialManager"}, {"backtrace": 68, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/USBAudioManager"}, {"backtrace": 69, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/Updater"}, {"backtrace": 71, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/Updater/UpdaterBase"}, {"backtrace": 72, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/Updater/UpdaterFactory"}, {"backtrace": 73, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/Updater/UpdaterFirmwareM1"}, {"backtrace": 74, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/Updater/UpdaterSoftware"}, {"backtrace": 75, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomFunction/Workspace"}, {"backtrace": 76, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget"}, {"backtrace": 78, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Battery"}, {"backtrace": 80, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Battery/BatteryS1M1"}, {"backtrace": 81, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/ButtonBox"}, {"backtrace": 83, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/ButtonBox/ButtonBoxS1M1"}, {"backtrace": 84, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Chart"}, {"backtrace": 85, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Circle"}, {"backtrace": 87, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Circle/CircleS1M1"}, {"backtrace": 88, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/ComboBox"}, {"backtrace": 90, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/ComboBox/ComboBoxS1M1"}, {"backtrace": 91, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/ComboBox/ComboBoxS1M2"}, {"backtrace": 92, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/ComboBox/ComboBoxS1M3"}, {"backtrace": 93, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Dial"}, {"backtrace": 95, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M1"}, {"backtrace": 96, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M2"}, {"backtrace": 97, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M3"}, {"backtrace": 98, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M4"}, {"backtrace": 99, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M5"}, {"backtrace": 100, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M6"}, {"backtrace": 101, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/EqualizerController"}, {"backtrace": 103, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/EqualizerController/EqualizerControllerS1M1"}, {"backtrace": 104, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/FramelessWindow"}, {"backtrace": 105, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Menu"}, {"backtrace": 107, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Menu/MenuS1M1"}, {"backtrace": 108, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox"}, {"backtrace": 110, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxS1M1"}, {"backtrace": 111, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxS2M1"}, {"backtrace": 112, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxS3M1"}, {"backtrace": 113, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxWidget"}, {"backtrace": 115, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget1"}, {"backtrace": 116, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget2"}, {"backtrace": 117, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget3"}, {"backtrace": 118, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget4"}, {"backtrace": 119, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton"}, {"backtrace": 121, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M1"}, {"backtrace": 122, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M10"}, {"backtrace": 123, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M11"}, {"backtrace": 124, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M12"}, {"backtrace": 125, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M13"}, {"backtrace": 126, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M14"}, {"backtrace": 127, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M15"}, {"backtrace": 128, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M2"}, {"backtrace": 129, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M3"}, {"backtrace": 130, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M4"}, {"backtrace": 131, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M5"}, {"backtrace": 132, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M6"}, {"backtrace": 133, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M7"}, {"backtrace": 134, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M8"}, {"backtrace": 135, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M9"}, {"backtrace": 136, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup"}, {"backtrace": 138, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M1"}, {"backtrace": 139, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M10"}, {"backtrace": 140, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M11"}, {"backtrace": 141, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M12"}, {"backtrace": 142, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M2"}, {"backtrace": 143, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M3"}, {"backtrace": 144, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M4"}, {"backtrace": 145, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M5"}, {"backtrace": 146, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M6"}, {"backtrace": 147, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M7"}, {"backtrace": 148, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M8"}, {"backtrace": 149, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M9"}, {"backtrace": 150, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Slider"}, {"backtrace": 152, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/HSlider"}, {"backtrace": 154, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/HSlider/HSliderS1M1"}, {"backtrace": 155, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/HSlider/HSliderS2M1"}, {"backtrace": 156, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/VSlider"}, {"backtrace": 158, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/VSlider/VSliderS1M1"}, {"backtrace": 159, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/VSlider/VSliderS1M2"}, {"backtrace": 160, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/TabWidget"}, {"backtrace": 162, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/TabWidget/TabWidgetS1M1"}, {"backtrace": 163, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/ToolButton"}, {"backtrace": 165, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/ToolButton/ToolButtonS1M1"}, {"backtrace": 166, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter"}, {"backtrace": 168, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M1"}, {"backtrace": 169, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M2"}, {"backtrace": 170, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M3"}, {"backtrace": 171, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M4"}, {"backtrace": 172, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M5"}, {"backtrace": 173, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M6"}, {"backtrace": 174, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M7"}, {"backtrace": 175, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M8"}, {"backtrace": 176, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS2M1"}, {"backtrace": 177, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceConnector"}, {"backtrace": 179, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceConnector/DeviceConnectorView"}, {"backtrace": 181, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceConnector/DeviceConnectorView/DeviceConnectorViewBase"}, {"backtrace": 182, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceConnector/DeviceConnectorView/DeviceConnectorViewS1M1"}, {"backtrace": 183, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField"}, {"backtrace": 185, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldEffect"}, {"backtrace": 187, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldEffect/FieldEffectBase1"}, {"backtrace": 188, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldEffect/FieldEffectS1M1"}, {"backtrace": 189, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldHead"}, {"backtrace": 191, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldHead/FieldHeadBase1"}, {"backtrace": 192, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldHead/FieldHeadBase2"}, {"backtrace": 193, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldHead/FieldHeadS1M1"}, {"backtrace": 194, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldHead/FieldHeadS1M2"}, {"backtrace": 195, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldInput"}, {"backtrace": 197, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldInput/FieldInputBase1"}, {"backtrace": 198, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldInput/FieldInputS1M1"}, {"backtrace": 199, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldLoopback"}, {"backtrace": 201, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldLoopback/FieldLoopbackBase1"}, {"backtrace": 202, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldLoopback/FieldLoopbackS1M1"}, {"backtrace": 203, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldMixer"}, {"backtrace": 205, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldMixer/FieldMixerBase1"}, {"backtrace": 206, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldMixer/FieldMixerS1M1"}, {"backtrace": 207, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin"}, {"backtrace": 209, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin/FieldOriginBase1"}, {"backtrace": 210, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin/FieldOriginBase2"}, {"backtrace": 211, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin/FieldOriginBase3"}, {"backtrace": 212, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin/FieldOriginS1M1"}, {"backtrace": 213, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin/FieldOriginS1M2"}, {"backtrace": 214, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin/FieldOriginS2M1"}, {"backtrace": 215, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldOutput"}, {"backtrace": 217, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldOutput/FieldOutputBase1"}, {"backtrace": 218, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceField/FieldOutput/FieldOutputS1M1"}, {"backtrace": 219, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceMainWindow"}, {"backtrace": 221, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceMainWindow/MainWindow_Base"}, {"backtrace": 222, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceMainWindow/MainWindow_M62"}, {"backtrace": 223, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget"}, {"backtrace": 225, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetAutoGain"}, {"backtrace": 227, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetAutoGain/AutoGainS1M1"}, {"backtrace": 228, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEffect"}, {"backtrace": 230, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEffect/EffectBase"}, {"backtrace": 231, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEffect/EffectS1M1"}, {"backtrace": 232, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEffect/EffectS1M2"}, {"backtrace": 233, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEffect/EffectS1M3"}, {"backtrace": 234, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEffect/EffectS1M4"}, {"backtrace": 235, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEqualizer"}, {"backtrace": 237, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEqualizer/EqualizerBase"}, {"backtrace": 238, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEqualizer/EqualizerPanelS1M1"}, {"backtrace": 239, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEqualizer/EqualizerS1M1"}, {"backtrace": 240, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEqualizer/EqualizerS1M2"}, {"backtrace": 241, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput"}, {"backtrace": 243, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputBase"}, {"backtrace": 244, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M1"}, {"backtrace": 245, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M2"}, {"backtrace": 246, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M3"}, {"backtrace": 247, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M4"}, {"backtrace": 248, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M5"}, {"backtrace": 249, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M6"}, {"backtrace": 250, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS2M1"}, {"backtrace": 251, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS2M2"}, {"backtrace": 252, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetLoopback"}, {"backtrace": 254, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetLoopback/LoopbackBase"}, {"backtrace": 255, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetLoopback/LoopbackS1M1"}, {"backtrace": 256, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetLoopback/LoopbackS1M2"}, {"backtrace": 257, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer"}, {"backtrace": 259, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer/MixerBase"}, {"backtrace": 260, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer/MixerS1M1"}, {"backtrace": 261, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer/MixerS1M2"}, {"backtrace": 262, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer/MixerS1M3"}, {"backtrace": 263, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer/MixerS1M4"}, {"backtrace": 264, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin"}, {"backtrace": 266, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginBase"}, {"backtrace": 267, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M1"}, {"backtrace": 268, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M10"}, {"backtrace": 269, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M11"}, {"backtrace": 270, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M12"}, {"backtrace": 271, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M13"}, {"backtrace": 272, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M2"}, {"backtrace": 273, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M3"}, {"backtrace": 274, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M4"}, {"backtrace": 275, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M6"}, {"backtrace": 276, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M7"}, {"backtrace": 277, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M8"}, {"backtrace": 278, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M9"}, {"backtrace": 279, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOutput"}, {"backtrace": 281, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOutput/OutputBase"}, {"backtrace": 282, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOutput/OutputS1M1"}, {"backtrace": 283, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOutput/OutputS1M2"}, {"backtrace": 284, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOutput/OutputS1M3"}, {"backtrace": 285, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOutput/OutputS1M4"}, {"backtrace": 286, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate"}, {"backtrace": 288, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/DeviceAll"}, {"backtrace": 290, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/DeviceAll/WidgetAbout1"}, {"backtrace": 291, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/DeviceAll/WidgetAudio1"}, {"backtrace": 292, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/DeviceAll/WidgetSystem1"}, {"backtrace": 293, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/M62"}, {"backtrace": 295, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget1"}, {"backtrace": 296, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget2"}, {"backtrace": 297, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget3"}, {"backtrace": 298, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget5"}, {"backtrace": 299, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget6"}, {"backtrace": 300, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget7"}, {"backtrace": 301, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/ThirdPartyResource"}, {"backtrace": 303, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/ThirdPartyResource/Component"}, {"backtrace": 305, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/ThirdPartyResource/Component/TKSpline"}, {"backtrace": 306, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/ThirdPartyResource/Font"}, {"backtrace": 307, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/ThirdPartyResource/Icon"}, {"backtrace": 308, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/ThirdPartyResource/Image"}, {"backtrace": 310, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/ThirdPartyResource/Image/PushButtonGroup"}, {"backtrace": 311, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/USBAudio"}, {"backtrace": 313, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/USBAudio/API"}, {"backtrace": 314, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/USBAudio/SDK"}, {"backtrace": 316, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/USBAudio/SDK/MAC"}, {"backtrace": 317, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/USBAudio/SDK/WIN"}, {"backtrace": 318, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/USBHID"}, {"backtrace": 320, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/USBHID/API"}, {"backtrace": 321, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/USBHID/Device"}, {"backtrace": 323, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/USBHID/Device/DeviceBase"}, {"backtrace": 324, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/USBHID/Device/DeviceM62"}, {"backtrace": 325, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/USBHID/SDK"}, {"backtrace": 327, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/USBHID/SDK/linux"}, {"backtrace": 328, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/USBHID/SDK/mac"}, {"backtrace": 329, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/Source/USBHID/SDK/win"}, {"backtrace": 330, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/3d59d5"}, {"backtrace": 331, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/d32d81"}, {"backtrace": 332, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/9932bd"}, {"backtrace": 333, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/8dec82"}, {"backtrace": 334, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/1ae6f3"}, {"backtrace": 335, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/3e117d"}, {"backtrace": 336, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/409ced"}, {"backtrace": 337, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/dd3f7a"}, {"backtrace": 338, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/0d6c37"}, {"backtrace": 339, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/9f94b4"}, {"backtrace": 340, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/3ce7e0"}, {"backtrace": 341, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/325c6f"}, {"backtrace": 342, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/8b1eee"}, {"backtrace": 343, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/b433c2"}, {"backtrace": 344, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/97c906"}, {"backtrace": 345, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/973edf"}, {"backtrace": 346, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/b1469a"}, {"backtrace": 347, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/8dbe06"}, {"backtrace": 348, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/c7545b"}, {"backtrace": 349, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/51133f"}, {"backtrace": 350, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/8e3cee"}, {"backtrace": 351, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/fb6557"}, {"backtrace": 352, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/77bb6e"}, {"backtrace": 353, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/3463eb"}, {"backtrace": 354, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/88c8aa"}, {"backtrace": 355, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/38583a"}, {"backtrace": 356, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/951c6c"}, {"backtrace": 357, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/9e24b0"}, {"backtrace": 358, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/f74d14"}, {"backtrace": 359, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/6bb67c"}, {"backtrace": 360, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/e2bf37"}, {"backtrace": 361, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/47bd68"}, {"backtrace": 362, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/3b9917"}, {"backtrace": 363, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/811480"}, {"backtrace": 364, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/ec314e"}, {"backtrace": 365, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/6293d7"}, {"backtrace": 366, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/cf1354"}, {"backtrace": 367, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/3a1aa4"}, {"backtrace": 368, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/825ead"}, {"backtrace": 369, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/77fc9c"}, {"backtrace": 370, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/c58c53"}, {"backtrace": 371, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/45f5d9"}, {"backtrace": 372, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/873115"}, {"backtrace": 373, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/1ad9f8"}, {"backtrace": 374, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/d11dc0"}, {"backtrace": 375, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/e42e24"}, {"backtrace": 376, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/d0d34e"}, {"backtrace": 377, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/dc5571"}, {"backtrace": 378, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/0a4477"}, {"backtrace": 379, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/7dbb21"}, {"backtrace": 380, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/cdde82"}, {"backtrace": 381, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/23a48c"}, {"backtrace": 382, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/aa53b8"}, {"backtrace": 383, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/94bb1c"}, {"backtrace": 384, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/246d94"}, {"backtrace": 385, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/cac869"}, {"backtrace": 386, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/ab8e07"}, {"backtrace": 387, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/2fb990"}, {"backtrace": 388, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/0da7eb"}, {"backtrace": 389, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/c40ae0"}, {"backtrace": 390, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/f6e8f4"}, {"backtrace": 391, "isSystem": true, "path": "C:/Users/<USER>/Documents/ToppingProfessionalControlCenter/build/Source/.qt/9ddbde"}, {"backtrace": 24, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtCore"}, {"backtrace": 24, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include"}, {"backtrace": 24, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/mkspecs/win32-msvc"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtWidgets"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtGui"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtNetwork"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtCharts"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtOpenGL"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtOpenGLWidgets"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtSvg"}], "language": "C", "sourceIndexes": [411]}], "dependencies": [{"backtrace": 51, "id": "TPCC_ui_property_check::@43690dd2fb94c8e45e84"}, {"id": "TPCC_autogen_timestamp_deps::@43690dd2fb94c8e45e84"}, {"backtrace": 0, "id": "TPCC_autogen::@43690dd2fb94c8e45e84"}], "id": "TPCC::@43690dd2fb94c8e45e84", "install": {"destinations": [{"backtrace": 5, "path": "bin"}], "prefix": {"path": "C:/Program Files (x86)/TPCC"}}, "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG /Zi /Ob0 /Od /RTC1 -MDd", "role": "flags"}, {"fragment": "/machine:x64 /debug /INCREMENTAL /subsystem:windows", "role": "flags"}, {"backtrace": 6, "fragment": "D:\\Qt\\6.9.0\\msvc2022_64\\lib\\Qt6Networkd.lib", "role": "libraries"}, {"backtrace": 6, "fragment": "D:\\Qt\\6.9.0\\msvc2022_64\\lib\\Qt6Chartsd.lib", "role": "libraries"}, {"backtrace": 6, "fragment": "D:\\Qt\\6.9.0\\msvc2022_64\\lib\\Qt6Svgd.lib", "role": "libraries"}, {"backtrace": 13, "fragment": "ws2_32.lib", "role": "libraries"}, {"backtrace": 18, "fragment": "D:\\Qt\\6.9.0\\msvc2022_64\\lib\\Qt6OpenGLWidgetsd.lib", "role": "libraries"}, {"backtrace": 6, "fragment": "D:\\Qt\\6.9.0\\msvc2022_64\\lib\\Qt6Widgetsd.lib", "role": "libraries"}, {"backtrace": 18, "fragment": "D:\\Qt\\6.9.0\\msvc2022_64\\lib\\Qt6OpenGLd.lib", "role": "libraries"}, {"backtrace": 18, "fragment": "user32.lib", "role": "libraries"}, {"backtrace": 23, "fragment": "D:\\Qt\\6.9.0\\msvc2022_64\\lib\\Qt6Guid.lib", "role": "libraries"}, {"backtrace": 24, "fragment": "D:\\Qt\\6.9.0\\msvc2022_64\\lib\\Qt6Cored.lib", "role": "libraries"}, {"backtrace": 29, "fragment": "mpr.lib", "role": "libraries"}, {"backtrace": 29, "fragment": "userenv.lib", "role": "libraries"}, {"backtrace": 38, "fragment": "D:\\Qt\\6.9.0\\msvc2022_64\\lib\\Qt6EntryPointd.lib", "role": "libraries"}, {"backtrace": 39, "fragment": "shell32.lib", "role": "libraries"}, {"backtrace": 48, "fragment": "d3d11.lib", "role": "libraries"}, {"backtrace": 48, "fragment": "dxgi.lib", "role": "libraries"}, {"backtrace": 48, "fragment": "dxguid.lib", "role": "libraries"}, {"backtrace": 48, "fragment": "d3d12.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "TPCC", "nameOnDisk": "M Control Center.exe", "paths": {"build": "Source", "source": "Source"}, "sourceGroups": [{"name": "Source Files\\Generated", "sourceIndexes": [0, 550, 551]}, {"name": "Source Files", "sourceIndexes": [1, 6, 8, 10, 13, 15, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 65, 68, 71, 74, 76, 79, 82, 85, 87, 90, 93, 96, 99, 102, 105, 108, 111, 113, 116, 119, 121, 124, 127, 130, 133, 135, 138, 141, 144, 147, 150, 153, 155, 158, 161, 164, 167, 170, 172, 175, 178, 181, 184, 187, 190, 193, 194, 199, 201, 204, 206, 208, 211, 212, 215, 218, 220, 222, 224, 226, 228, 230, 232, 234, 236, 238, 240, 241, 245, 247, 249, 251, 253, 255, 258, 261, 264, 267, 269, 271, 273, 275, 277, 279, 281, 283, 285, 287, 289, 291, 293, 295, 297, 300, 303, 306, 309, 312, 315, 318, 321, 324, 327, 330, 333, 335, 337, 339, 341, 343, 345, 347, 349, 351, 353, 355, 357, 359, 361, 364, 366, 368, 372, 374, 376, 378, 380, 382, 384, 386, 389, 391, 393, 395, 396, 399, 401, 410, 411, 412, 413, 414, 415, 416, 417, 418, 419, 420, 421, 422, 423, 424, 425, 426, 427, 428, 429, 430, 431, 432, 486]}, {"name": "", "sourceIndexes": [2, 3, 4, 5, 12, 17, 64, 67, 70, 73, 78, 81, 84, 89, 92, 95, 98, 101, 104, 107, 110, 115, 118, 123, 126, 129, 132, 137, 140, 143, 146, 149, 152, 157, 160, 163, 166, 169, 174, 177, 180, 183, 186, 189, 192, 197, 198, 203, 210, 217, 257, 260, 263, 266, 299, 302, 305, 308, 311, 314, 317, 320, 323, 326, 329, 332, 363]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [7, 9, 11, 14, 16, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 66, 69, 72, 75, 77, 80, 83, 86, 88, 91, 94, 97, 100, 103, 106, 109, 112, 114, 117, 120, 122, 125, 128, 131, 134, 136, 139, 142, 145, 148, 151, 154, 156, 159, 162, 165, 168, 171, 173, 176, 179, 182, 185, 188, 191, 195, 196, 200, 202, 205, 207, 209, 213, 214, 216, 219, 221, 223, 225, 227, 229, 231, 233, 235, 237, 239, 242, 243, 244, 246, 248, 250, 252, 254, 256, 259, 262, 265, 268, 270, 272, 274, 276, 278, 280, 282, 284, 286, 288, 290, 292, 294, 296, 298, 301, 304, 307, 310, 313, 316, 319, 322, 325, 328, 331, 334, 336, 338, 340, 342, 344, 346, 348, 350, 352, 354, 356, 358, 360, 362, 365, 367, 369, 370, 371, 373, 375, 377, 379, 381, 383, 385, 387, 388, 390, 392, 394, 397, 398, 400, 402, 403, 404, 405, 406, 407, 408, 409, 433, 434, 435, 436, 437, 438, 439, 440, 441, 442, 443, 444, 445, 446, 447, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466, 467, 468, 469, 470, 471, 472, 473, 474, 475, 476, 477, 478, 479, 480, 481, 482, 483, 484, 485, 487, 488, 489, 490, 491, 492, 493, 494, 495, 496, 497, 498, 499, 500, 501, 502, 503, 504, 505, 506, 507, 508, 509, 510, 511, 512, 513, 514, 515, 516, 517, 518, 519, 520, 521, 522, 523, 524, 525, 526, 527, 528, 529, 530, 531, 532, 533, 534, 535, 536, 537, 538, 539, 540, 541, 542, 543, 544, 545, 546, 547, 548, 549]}, {"name": "CMake Rules", "sourceIndexes": [552, 553]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/Source/TPCC_autogen/mocs_compilation.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/main.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Updater/SeriesM/UpdaterSeriesM.json", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Updater/SeriesM/UpdaterSeriesMTest.json", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Updater/SeriesS/UpdaterSeriesS.json", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Updater/SeriesS/UpdaterSeriesSTest.json", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceConnector/deviceconnector.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceConnector/deviceconnector.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceConnector/DeviceConnectorView/DeviceConnectorViewBase/deviceconnectorviewbase.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceConnector/DeviceConnectorView/DeviceConnectorViewBase/deviceconnectorviewbase.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceConnector/DeviceConnectorView/DeviceConnectorViewS1M1/deviceconnectorviews1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceConnector/DeviceConnectorView/DeviceConnectorViewS1M1/deviceconnectorviews1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceConnector/DeviceConnectorView/DeviceConnectorViewS1M1/deviceconnectorviews1m1.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceMainWindow/MainWindow_Base/mainwindow_base.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceMainWindow/MainWindow_Base/mainwindow_base.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceMainWindow/MainWindow_M62/mainwindow_m62.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceMainWindow/MainWindow_M62/mainwindow_m62.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceMainWindow/MainWindow_M62/mainwindow_m62.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldEffect/FieldEffectBase1/fieldeffectbase1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldEffect/FieldEffectBase1/fieldeffectbase1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldEffect/FieldEffectS1M1/fieldeffects1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldEffect/FieldEffectS1M1/fieldeffects1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldHead/FieldHeadBase1/fieldheadbase1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldHead/FieldHeadBase1/fieldheadbase1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldHead/FieldHeadBase2/fieldheadbase2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldHead/FieldHeadBase2/fieldheadbase2.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldHead/FieldHeadS1M1/fieldheads1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldHead/FieldHeadS1M1/fieldheads1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldHead/FieldHeadS1M2/fieldheads1m2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldHead/FieldHeadS1M2/fieldheads1m2.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldInput/FieldInputBase1/fieldinputbase1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldInput/FieldInputBase1/fieldinputbase1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldInput/FieldInputS1M1/fieldinputs1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldInput/FieldInputS1M1/fieldinputs1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldLoopback/FieldLoopbackBase1/fieldloopbackbase1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldLoopback/FieldLoopbackBase1/fieldloopbackbase1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldLoopback/FieldLoopbackS1M1/fieldloopbacks1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldLoopback/FieldLoopbackS1M1/fieldloopbacks1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldMixer/FieldMixerBase1/fieldmixerbase1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldMixer/FieldMixerBase1/fieldmixerbase1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldMixer/FieldMixerS1M1/fieldmixers1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldMixer/FieldMixerS1M1/fieldmixers1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldOrigin/FieldOriginBase1/fieldoriginbase1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldOrigin/FieldOriginBase1/fieldoriginbase1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldOrigin/FieldOriginBase2/fieldoriginbase2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldOrigin/FieldOriginBase2/fieldoriginbase2.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldOrigin/FieldOriginBase3/fieldoriginbase3.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldOrigin/FieldOriginBase3/fieldoriginbase3.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldOrigin/FieldOriginS1M1/fieldorigins1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldOrigin/FieldOriginS1M1/fieldorigins1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldOrigin/FieldOriginS1M2/fieldorigins1m2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldOrigin/FieldOriginS1M2/fieldorigins1m2.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldOrigin/FieldOriginS2M1/fieldorigins2m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldOrigin/FieldOriginS2M1/fieldorigins2m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldOutput/FieldOutputBase1/fieldoutputbase1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldOutput/FieldOutputBase1/fieldoutputbase1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldOutput/FieldOutputS1M1/fieldoutputs1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldOutput/FieldOutputS1M1/fieldoutputs1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetAutoGain/AutoGainS1M1/autogains1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetAutoGain/AutoGainS1M1/autogains1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetEffect/EffectBase/effectbase.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetEffect/EffectBase/effectbase.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetEffect/EffectS1M1/effects1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetEffect/EffectS1M1/effects1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetEffect/EffectS1M1/effects1m1.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetEffect/EffectS1M2/effects1m2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetEffect/EffectS1M2/effects1m2.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetEffect/EffectS1M2/effects1m2.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetEffect/EffectS1M3/effects1m3.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetEffect/EffectS1M3/effects1m3.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetEffect/EffectS1M3/effects1m3.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetEffect/EffectS1M4/effects1m4.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetEffect/EffectS1M4/effects1m4.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetEffect/EffectS1M4/effects1m4.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetEqualizer/EqualizerBase/equalizerbase.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetEqualizer/EqualizerBase/equalizerbase.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetEqualizer/EqualizerPanelS1M1/equalizerpanels1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetEqualizer/EqualizerPanelS1M1/equalizerpanels1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetEqualizer/EqualizerPanelS1M1/equalizerpanels1m1.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetEqualizer/EqualizerS1M1/equalizers1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetEqualizer/EqualizerS1M1/equalizers1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetEqualizer/EqualizerS1M1/equalizers1m1.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetEqualizer/EqualizerS1M2/equalizers1m2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetEqualizer/EqualizerS1M2/equalizers1m2.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetEqualizer/EqualizerS1M2/equalizers1m2.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetInput/InputBase/inputbase.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetInput/InputBase/inputbase.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetInput/InputS1M1/inputs1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetInput/InputS1M1/inputs1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetInput/InputS1M1/inputs1m1.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetInput/InputS1M2/inputs1m2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetInput/InputS1M2/inputs1m2.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetInput/InputS1M2/inputs1m2.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetInput/InputS1M3/inputs1m3.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetInput/InputS1M3/inputs1m3.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetInput/InputS1M3/inputs1m3.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetInput/InputS1M4/inputs1m4.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetInput/InputS1M4/inputs1m4.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetInput/InputS1M4/inputs1m4.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetInput/InputS1M5/inputs1m5.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetInput/InputS1M5/inputs1m5.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetInput/InputS1M5/inputs1m5.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetInput/InputS1M6/inputs1m6.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetInput/InputS1M6/inputs1m6.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetInput/InputS1M6/inputs1m6.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetInput/InputS2M1/inputs2m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetInput/InputS2M1/inputs2m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetInput/InputS2M1/inputs2m1.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetInput/InputS2M2/inputs2m2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetInput/InputS2M2/inputs2m2.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetInput/InputS2M2/inputs2m2.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetLoopback/LoopbackBase/loopbackbase.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetLoopback/LoopbackBase/loopbackbase.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetLoopback/LoopbackS1M1/loopbacks1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetLoopback/LoopbackS1M1/loopbacks1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetLoopback/LoopbackS1M1/loopbacks1m1.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetLoopback/LoopbackS1M2/loopbacks1m2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetLoopback/LoopbackS1M2/loopbacks1m2.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetLoopback/LoopbackS1M2/loopbacks1m2.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetMixer/MixerBase/mixerbase.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetMixer/MixerBase/mixerbase.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetMixer/MixerS1M1/mixers1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetMixer/MixerS1M1/mixers1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetMixer/MixerS1M1/mixers1m1.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetMixer/MixerS1M2/mixers1m2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetMixer/MixerS1M2/mixers1m2.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetMixer/MixerS1M2/mixers1m2.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetMixer/MixerS1M3/mixers1m3.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetMixer/MixerS1M3/mixers1m3.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetMixer/MixerS1M3/mixers1m3.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetMixer/MixerS1M4/mixers1m4.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetMixer/MixerS1M4/mixers1m4.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetMixer/MixerS1M4/mixers1m4.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetOrigin/OriginBase/originbase.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginBase/originbase.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M1/origins1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M1/origins1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M1/origins1m1.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M10/origins1m10.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M10/origins1m10.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M10/origins1m10.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M11/origins1m11.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M11/origins1m11.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M11/origins1m11.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M12/origins1m12.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M12/origins1m12.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M12/origins1m12.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M13/origins1m13.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M13/origins1m13.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M13/origins1m13.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M2/origins1m2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M2/origins1m2.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M2/origins1m2.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M3/origins1m3.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M3/origins1m3.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M4/origins1m4.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M4/origins1m4.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M4/origins1m4.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M6/origins1m6.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M6/origins1m6.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M6/origins1m6.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M7/origins1m7.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M7/origins1m7.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M7/origins1m7.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M8/origins1m8.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M8/origins1m8.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M8/origins1m8.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M9/origins1m9.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M9/origins1m9.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M9/origins1m9.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetOutput/OutputBase/outputbase.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOutput/OutputBase/outputbase.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetOutput/OutputS1M1/outputs1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOutput/OutputS1M1/outputs1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOutput/OutputS1M1/outputs1m1.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetOutput/OutputS1M2/outputs1m2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOutput/OutputS1M2/outputs1m2.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOutput/OutputS1M2/outputs1m2.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetOutput/OutputS1M3/outputs1m3.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOutput/OutputS1M3/outputs1m3.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOutput/OutputS1M3/outputs1m3.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetOutput/OutputS1M4/outputs1m4.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOutput/OutputS1M4/outputs1m4.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOutput/OutputS1M4/outputs1m4.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetPrivate/DeviceAll/WidgetAbout1/widgetabout1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetPrivate/DeviceAll/WidgetAbout1/widgetabout1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetPrivate/DeviceAll/WidgetAbout1/widgetabout1.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetPrivate/DeviceAll/WidgetAudio1/widgetaudio1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetPrivate/DeviceAll/WidgetAudio1/widgetaudio1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetPrivate/DeviceAll/WidgetAudio1/widgetaudio1.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetPrivate/DeviceAll/WidgetSystem1/widgetsytem1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetPrivate/DeviceAll/WidgetSystem1/widgetsytem1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetPrivate/DeviceAll/WidgetSystem1/widgetsytem1.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget1/m62_privatewidget1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget1/m62_privatewidget1_1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget1/m62_privatewidget1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget1/m62_privatewidget1_1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget1/m62_privatewidget1.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget1/m62_privatewidget1_1.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget2/m62_privatewidget2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget2/m62_privatewidget2.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget3/m62_privatewidget3.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget3/m62_privatewidget3.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget3/m62_privatewidget3.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget5/m62_privatewidget5.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget5/m62_privatewidget5.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget6/m62_privatewidget6.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget6/m62_privatewidget6.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget7/m62_privatewidget7.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget7/m62_privatewidget7.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget7/m62_privatewidget7.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/Battery/BatteryS1M1/batterydrawstrategy.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/Battery/BatteryS1M1/batterys1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/Battery/BatteryS1M1/batterydrawstrategy.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/CustomWidget/Battery/BatteryS1M1/batterys1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/ButtonBox/ButtonBoxS1M1/buttonboxs1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/ButtonBox/ButtonBoxS1M1/buttonboxs1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/CustomWidget/ButtonBox/ButtonBoxS1M1/buttonboxs1m1.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/Chart/chart.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/Chart/chart.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/Circle/CircleS1M1/circles1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/Circle/CircleS1M1/circles1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/ComboBox/ComboBoxS1M1/comboboxs1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/ComboBox/ComboBoxS1M1/comboboxs1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/ComboBox/ComboBoxS1M2/comboboxs1m2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/ComboBox/ComboBoxS1M2/comboboxs1m2.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/ComboBox/ComboBoxS1M3/comboboxs1m3.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/ComboBox/ComboBoxS1M3/comboboxs1m3.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/Dial/DialS1M1/dials1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/Dial/DialS1M1/dials1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/Dial/DialS1M2/dials1m2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/Dial/DialS1M2/dials1m2.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/Dial/DialS1M3/dials1m3.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/Dial/DialS1M3/dials1m3.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/Dial/DialS1M4/dials1m4.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/Dial/DialS1M4/dials1m4.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/Dial/DialS1M5/dials1m5.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/Dial/DialS1M5/dials1m5.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/Dial/DialS1M6/dials1m6.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/Dial/DialS1M6/dials1m6.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/EqualizerController/EqualizerControllerS1M1/equalizercontrollers1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/EqualizerController/EqualizerControllerS1M1/eqwidgetiem.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/EqualizerController/EqualizerControllerS1M1/equalizercontrollers1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/CustomWidget/EqualizerController/EqualizerControllerS1M1/eqwidgetiem.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/CustomWidget/EqualizerController/EqualizerControllerS1M1/eqwidgetitemdata.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/FramelessWindow/framelesswindow.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/FramelessWindow/framelesswindow.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/Menu/MenuS1M1/menus1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/Menu/MenuS1M1/menus1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/MessageBox/MessageBoxS1M1/messageboxs1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/MessageBox/MessageBoxS1M1/messageboxs1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/MessageBox/MessageBoxS2M1/messageboxs2m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/MessageBox/MessageBoxS2M1/messageboxs2m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/MessageBox/MessageBoxS3M1/messageboxs3m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/MessageBox/MessageBoxS3M1/messageboxs3m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget1/messageboxwidget1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget1/messageboxwidget1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget1/messageboxwidget1.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget2/messageboxwidget2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget2/messageboxwidget2.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget2/messageboxwidget2.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget3/messageboxwidget3.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget3/messageboxwidget3.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget3/messageboxwidget3.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget4/messageboxwidget4.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget4/messageboxwidget4.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget4/messageboxwidget4.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButton/PushButtonS1M1/pushbuttons1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButton/PushButtonS1M1/pushbuttons1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButton/PushButtonS1M10/pushbuttons1m10.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButton/PushButtonS1M10/pushbuttons1m10.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButton/PushButtonS1M11/pushbuttons1m11.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButton/PushButtonS1M11/pushbuttons1m11.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButton/PushButtonS1M12/pushbuttons1m12.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButton/PushButtonS1M12/pushbuttons1m12.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButton/PushButtonS1M13/pushbuttons1m13.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButton/PushButtonS1M13/pushbuttons1m13.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButton/PushButtonS1M14/pushbuttons1m14.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButton/PushButtonS1M14/pushbuttons1m14.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButton/PushButtonS1M15/pushbuttons1m15.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButton/PushButtonS1M15/pushbuttons1m15.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButton/PushButtonS1M2/pushbuttons1m2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButton/PushButtonS1M2/pushbuttons1m2.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButton/PushButtonS1M3/pushbuttons1m3.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButton/PushButtonS1M3/pushbuttons1m3.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButton/PushButtonS1M4/pushbuttons1m4.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButton/PushButtonS1M4/pushbuttons1m4.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButton/PushButtonS1M5/pushbuttons1m5.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButton/PushButtonS1M5/pushbuttons1m5.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButton/PushButtonS1M6/pushbuttons1m6.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButton/PushButtonS1M6/pushbuttons1m6.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButton/PushButtonS1M7/pushbuttons1m7.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButton/PushButtonS1M7/pushbuttons1m7.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButton/PushButtonS1M8/pushbuttons1m8.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButton/PushButtonS1M8/pushbuttons1m8.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButton/PushButtonS1M9/pushbuttons1m9.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButton/PushButtonS1M9/pushbuttons1m9.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M1/pushbuttongroups1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M1/pushbuttongroups1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M1/pushbuttongroups1m1.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M10/pushbuttongroups1m10.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M10/pushbuttongroups1m10.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M10/pushbuttongroups1m10.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M11/pushbuttongroups1m11.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M11/pushbuttongroups1m11.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M11/pushbuttongroups1m11.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M12/pushbuttongroups1m12.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M12/pushbuttongroups1m12.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M12/pushbuttongroups1m12.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M2/pushbuttongroups1m2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M2/pushbuttongroups1m2.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M2/pushbuttongroups1m2.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M3/pushbuttongroups1m3.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M3/pushbuttongroups1m3.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M3/pushbuttongroups1m3.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M4/pushbuttongroups1m4.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M4/pushbuttongroups1m4.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M4/pushbuttongroups1m4.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M5/pushbuttongroups1m5.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M5/pushbuttongroups1m5.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M5/pushbuttongroups1m5.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M6/pushbuttongroups1m6.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M6/pushbuttongroups1m6.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M6/pushbuttongroups1m6.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M7/pushbuttongroups1m7.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M7/pushbuttongroups1m7.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M7/pushbuttongroups1m7.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M8/pushbuttongroups1m8.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M8/pushbuttongroups1m8.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M8/pushbuttongroups1m8.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M9/pushbuttongroups1m9.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M9/pushbuttongroups1m9.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M9/pushbuttongroups1m9.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/Slider/HSlider/HSliderS1M1/hsliders1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/Slider/HSlider/HSliderS1M1/hsliders1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/Slider/HSlider/HSliderS2M1/hsliders2m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/Slider/HSlider/HSliderS2M1/hsliders2m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/Slider/VSlider/VSliderS1M1/vsliders1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/Slider/VSlider/VSliderS1M1/vsliders1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/Slider/VSlider/VSliderS1M2/vsliders1m2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/Slider/VSlider/VSliderS1M2/vsliders1m2.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/TabWidget/TabWidgetS1M1/tabwidgets1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/TabWidget/TabWidgetS1M1/tabwidgets1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/ToolButton/ToolButtonS1M1/toolbuttons1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/ToolButton/ToolButtonS1M1/toolbuttons1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS1M1/volumemeters1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS1M1/volumemeters1m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS1M2/volumemeters1m2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS1M2/volumemeters1m2.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS1M3/volumemeters1m3.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS1M3/volumemeters1m3.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS1M4/volumemeters1m4.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS1M4/volumemeters1m4.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS1M5/volumemeters1m5.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS1M5/volumemeters1m5.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS1M6/volumemeters1m6.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS1M6/volumemeters1m6.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS1M7/volumemeters1m7.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS1M7/volumemeters1m7.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS1M8/volumemeters1m8.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS1M8/volumemeters1m8.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS2M1/volumemeters2m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS2M1/volumemeters2m1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/ThirdPartyResource/ThirdPartyResource.qrc", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 1, "path": "Source/ThirdPartyResource/AppIconWin.rc", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/ThirdPartyResource/Component/TKSpline/tkspline.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomFunction/AppSettings/appsettings.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomFunction/AppSettings/appsettings.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomFunction/AutoStartManager/autostartmanager.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomFunction/AutoStartManager/autostartmanager.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/CustomFunction/CTL/BlockingQueue/blockingqueue.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/CustomFunction/CTL/Singleton/singleton.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomFunction/DebugManager/debugmanager.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomFunction/DebugManager/debugmanager.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomFunction/EqualizerTool/equalizertool.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomFunction/EqualizerTool/equalizertool.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomFunction/GlobalFont/globalfont.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomFunction/GlobalFont/globalfont.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomFunction/SingleInstanceManager/singleinstancemanager.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomFunction/SingleInstanceManager/singleinstancemanager.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomFunction/Solo/solo.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomFunction/Solo/solo.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomFunction/TrialManager/trialmanager.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomFunction/TrialManager/trialmanager.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomFunction/USBAudioManager/usbaudiomanager.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomFunction/USBAudioManager/usbaudiomanager.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomFunction/Updater/UpdaterBase/updaterbase.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomFunction/Updater/UpdaterBase/updaterbase.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/CustomFunction/Updater/UpdaterFactory/updaterfactory.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomFunction/Updater/UpdaterFirmwareM1/updaterfirmwarem1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomFunction/Updater/UpdaterFirmwareM1/updaterfirmwarem1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomFunction/Updater/UpdaterSoftware/updatersoftware.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomFunction/Updater/UpdaterSoftware/updatersoftware.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomFunction/Workspace/workspace.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomFunction/Workspace/workspace.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBHID/Device/DeviceBase/devicebase.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBHID/Device/DeviceBase/devicetype1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/USBHID/Device/DeviceBase/devicebase.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBHID/Device/DeviceBase/devicetype1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBHID/Device/DeviceM62/devicem62.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/USBHID/Device/DeviceM62/devicem62.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBHID/API/usbhidapi.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/USBHID/API/usbhidapi.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBHID/SDK/hidapi.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBHID/SDK/hidapi_cfgmgr32.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBHID/SDK/hidapi_darwin.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBHID/SDK/hidapi_hidclass.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBHID/SDK/hidapi_hidpi.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBHID/SDK/hidapi_hidsdi.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBHID/SDK/hidapi_winapi.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Packager/Win/PackageManager.bat", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 2, "path": "Source/USBHID/SDK/win/hid.c", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/TUsbAudioApiDll.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/TUsbAudioApiExtendedInfo.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/TUsbAudioMixer.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/TbStdStringUtils.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/TbStringUtils.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/WnModuleFileName.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/WnRegistryKey.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/WnStringUtils.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/WnThread.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/WnTrace.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/WnTraceLogContext.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/WnTraceLogFile.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/WnTraceLogSettings.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/WnUiLanguage.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/WnUiLanguageFile.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/WnUiLanguageMgr.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/WnUiLanguageText.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/WnUiLanguageTextGroup.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/WnUserModeCrashDump.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/WnWow64.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/libtb_OSEnv_impl.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/CommonPluginProperties.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/MixerPluginProperties.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/TUsbAudioApiDll.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/TUsbAudioApiExtendedInfo.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/TUsbAudioMixer.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/TbOSEnv.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/TbStdStringUtils.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/TbStringUtils.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/TbUtils.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnCriticalSection.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnEvent.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnHandle.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnLibrary.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnModuleFileName.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnRegistryKey.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnStringUtils.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnStringUtils_impl.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnThread.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnTrace.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnTraceLogContext.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnTraceLogFile.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnTraceLogSettings.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnTypes.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnUiLanguage.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnUiLanguageFile.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnUiLanguageMgr.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnUiLanguageText.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnUiLanguageTextGroup.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnUserModeCrashDump.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnWow64.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/dsp_types.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/libbase.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/libtb.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/libtb_env.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/libwn.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/libwn_min_global.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/tbase_al.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/tbase_al_impl.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/tbase_al_impl_generic.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/tbase_pack1.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/tbase_packrestore.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/tbase_platform.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/tbase_types.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/tbase_utils.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/tstatus_codes.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/tstatus_codes_ex.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/tusb_cls_audio.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/tusb_cls_audio20.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/tusb_spec.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/tusbaudio_defs.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/tusbaudioapi.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/tusbaudioapi_defs.h", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/win_targetver.h", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/API/usbaudioapi.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/USBAudio/API/usbaudioapi.h", "sourceGroupIndex": 3}, {"backtrace": 392, "isGenerated": true, "path": "build/Source/.qt/3d59d5/ui_deviceconnectorviews1m1.h", "sourceGroupIndex": 3}, {"backtrace": 393, "isGenerated": true, "path": "build/Source/.qt/d32d81/ui_mainwindow_m62.h", "sourceGroupIndex": 3}, {"backtrace": 394, "isGenerated": true, "path": "build/Source/.qt/9932bd/ui_effects1m1.h", "sourceGroupIndex": 3}, {"backtrace": 395, "isGenerated": true, "path": "build/Source/.qt/8dec82/ui_effects1m2.h", "sourceGroupIndex": 3}, {"backtrace": 396, "isGenerated": true, "path": "build/Source/.qt/1ae6f3/ui_effects1m3.h", "sourceGroupIndex": 3}, {"backtrace": 397, "isGenerated": true, "path": "build/Source/.qt/3e117d/ui_effects1m4.h", "sourceGroupIndex": 3}, {"backtrace": 398, "isGenerated": true, "path": "build/Source/.qt/409ced/ui_equalizerpanels1m1.h", "sourceGroupIndex": 3}, {"backtrace": 399, "isGenerated": true, "path": "build/Source/.qt/dd3f7a/ui_equalizers1m1.h", "sourceGroupIndex": 3}, {"backtrace": 400, "isGenerated": true, "path": "build/Source/.qt/0d6c37/ui_equalizers1m2.h", "sourceGroupIndex": 3}, {"backtrace": 401, "isGenerated": true, "path": "build/Source/.qt/9f94b4/ui_inputs1m1.h", "sourceGroupIndex": 3}, {"backtrace": 402, "isGenerated": true, "path": "build/Source/.qt/3ce7e0/ui_inputs1m2.h", "sourceGroupIndex": 3}, {"backtrace": 403, "isGenerated": true, "path": "build/Source/.qt/325c6f/ui_inputs1m3.h", "sourceGroupIndex": 3}, {"backtrace": 404, "isGenerated": true, "path": "build/Source/.qt/8b1eee/ui_inputs1m4.h", "sourceGroupIndex": 3}, {"backtrace": 405, "isGenerated": true, "path": "build/Source/.qt/b433c2/ui_inputs1m5.h", "sourceGroupIndex": 3}, {"backtrace": 406, "isGenerated": true, "path": "build/Source/.qt/97c906/ui_inputs1m6.h", "sourceGroupIndex": 3}, {"backtrace": 407, "isGenerated": true, "path": "build/Source/.qt/973edf/ui_inputs2m1.h", "sourceGroupIndex": 3}, {"backtrace": 408, "isGenerated": true, "path": "build/Source/.qt/b1469a/ui_inputs2m2.h", "sourceGroupIndex": 3}, {"backtrace": 409, "isGenerated": true, "path": "build/Source/.qt/8dbe06/ui_loopbacks1m1.h", "sourceGroupIndex": 3}, {"backtrace": 410, "isGenerated": true, "path": "build/Source/.qt/c7545b/ui_loopbacks1m2.h", "sourceGroupIndex": 3}, {"backtrace": 411, "isGenerated": true, "path": "build/Source/.qt/51133f/ui_mixers1m1.h", "sourceGroupIndex": 3}, {"backtrace": 412, "isGenerated": true, "path": "build/Source/.qt/8e3cee/ui_mixers1m2.h", "sourceGroupIndex": 3}, {"backtrace": 413, "isGenerated": true, "path": "build/Source/.qt/fb6557/ui_mixers1m3.h", "sourceGroupIndex": 3}, {"backtrace": 414, "isGenerated": true, "path": "build/Source/.qt/77bb6e/ui_mixers1m4.h", "sourceGroupIndex": 3}, {"backtrace": 415, "isGenerated": true, "path": "build/Source/.qt/3463eb/ui_origins1m1.h", "sourceGroupIndex": 3}, {"backtrace": 416, "isGenerated": true, "path": "build/Source/.qt/88c8aa/ui_origins1m10.h", "sourceGroupIndex": 3}, {"backtrace": 417, "isGenerated": true, "path": "build/Source/.qt/38583a/ui_origins1m11.h", "sourceGroupIndex": 3}, {"backtrace": 418, "isGenerated": true, "path": "build/Source/.qt/951c6c/ui_origins1m12.h", "sourceGroupIndex": 3}, {"backtrace": 419, "isGenerated": true, "path": "build/Source/.qt/9e24b0/ui_origins1m13.h", "sourceGroupIndex": 3}, {"backtrace": 420, "isGenerated": true, "path": "build/Source/.qt/f74d14/ui_origins1m2.h", "sourceGroupIndex": 3}, {"backtrace": 421, "isGenerated": true, "path": "build/Source/.qt/6bb67c/ui_origins1m4.h", "sourceGroupIndex": 3}, {"backtrace": 422, "isGenerated": true, "path": "build/Source/.qt/e2bf37/ui_origins1m6.h", "sourceGroupIndex": 3}, {"backtrace": 423, "isGenerated": true, "path": "build/Source/.qt/47bd68/ui_origins1m7.h", "sourceGroupIndex": 3}, {"backtrace": 424, "isGenerated": true, "path": "build/Source/.qt/3b9917/ui_origins1m8.h", "sourceGroupIndex": 3}, {"backtrace": 425, "isGenerated": true, "path": "build/Source/.qt/811480/ui_origins1m9.h", "sourceGroupIndex": 3}, {"backtrace": 426, "isGenerated": true, "path": "build/Source/.qt/ec314e/ui_outputs1m1.h", "sourceGroupIndex": 3}, {"backtrace": 427, "isGenerated": true, "path": "build/Source/.qt/6293d7/ui_outputs1m2.h", "sourceGroupIndex": 3}, {"backtrace": 428, "isGenerated": true, "path": "build/Source/.qt/cf1354/ui_outputs1m3.h", "sourceGroupIndex": 3}, {"backtrace": 429, "isGenerated": true, "path": "build/Source/.qt/3a1aa4/ui_outputs1m4.h", "sourceGroupIndex": 3}, {"backtrace": 430, "isGenerated": true, "path": "build/Source/.qt/825ead/ui_widgetabout1.h", "sourceGroupIndex": 3}, {"backtrace": 431, "isGenerated": true, "path": "build/Source/.qt/77fc9c/ui_widgetaudio1.h", "sourceGroupIndex": 3}, {"backtrace": 432, "isGenerated": true, "path": "build/Source/.qt/c58c53/ui_widgetsytem1.h", "sourceGroupIndex": 3}, {"backtrace": 433, "isGenerated": true, "path": "build/Source/.qt/45f5d9/ui_m62_privatewidget1.h", "sourceGroupIndex": 3}, {"backtrace": 434, "isGenerated": true, "path": "build/Source/.qt/873115/ui_m62_privatewidget1_1.h", "sourceGroupIndex": 3}, {"backtrace": 435, "isGenerated": true, "path": "build/Source/.qt/1ad9f8/ui_m62_privatewidget3.h", "sourceGroupIndex": 3}, {"backtrace": 436, "isGenerated": true, "path": "build/Source/.qt/d11dc0/ui_m62_privatewidget7.h", "sourceGroupIndex": 3}, {"backtrace": 437, "isGenerated": true, "path": "build/Source/.qt/e42e24/ui_buttonboxs1m1.h", "sourceGroupIndex": 3}, {"backtrace": 438, "isGenerated": true, "path": "build/Source/.qt/d0d34e/ui_messageboxwidget1.h", "sourceGroupIndex": 3}, {"backtrace": 439, "isGenerated": true, "path": "build/Source/.qt/dc5571/ui_messageboxwidget2.h", "sourceGroupIndex": 3}, {"backtrace": 440, "isGenerated": true, "path": "build/Source/.qt/0a4477/ui_messageboxwidget3.h", "sourceGroupIndex": 3}, {"backtrace": 441, "isGenerated": true, "path": "build/Source/.qt/7dbb21/ui_messageboxwidget4.h", "sourceGroupIndex": 3}, {"backtrace": 442, "isGenerated": true, "path": "build/Source/.qt/cdde82/ui_pushbuttongroups1m1.h", "sourceGroupIndex": 3}, {"backtrace": 443, "isGenerated": true, "path": "build/Source/.qt/23a48c/ui_pushbuttongroups1m10.h", "sourceGroupIndex": 3}, {"backtrace": 444, "isGenerated": true, "path": "build/Source/.qt/aa53b8/ui_pushbuttongroups1m11.h", "sourceGroupIndex": 3}, {"backtrace": 445, "isGenerated": true, "path": "build/Source/.qt/94bb1c/ui_pushbuttongroups1m12.h", "sourceGroupIndex": 3}, {"backtrace": 446, "isGenerated": true, "path": "build/Source/.qt/246d94/ui_pushbuttongroups1m2.h", "sourceGroupIndex": 3}, {"backtrace": 447, "isGenerated": true, "path": "build/Source/.qt/cac869/ui_pushbuttongroups1m3.h", "sourceGroupIndex": 3}, {"backtrace": 448, "isGenerated": true, "path": "build/Source/.qt/ab8e07/ui_pushbuttongroups1m4.h", "sourceGroupIndex": 3}, {"backtrace": 449, "isGenerated": true, "path": "build/Source/.qt/2fb990/ui_pushbuttongroups1m5.h", "sourceGroupIndex": 3}, {"backtrace": 450, "isGenerated": true, "path": "build/Source/.qt/0da7eb/ui_pushbuttongroups1m6.h", "sourceGroupIndex": 3}, {"backtrace": 451, "isGenerated": true, "path": "build/Source/.qt/c40ae0/ui_pushbuttongroups1m7.h", "sourceGroupIndex": 3}, {"backtrace": 452, "isGenerated": true, "path": "build/Source/.qt/f6e8f4/ui_pushbuttongroups1m8.h", "sourceGroupIndex": 3}, {"backtrace": 453, "isGenerated": true, "path": "build/Source/.qt/9ddbde/ui_pushbuttongroups1m9.h", "sourceGroupIndex": 3}, {"backtrace": 0, "isGenerated": true, "path": "build/Source/TPCC_autogen/timestamp", "sourceGroupIndex": 0}, {"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/Source/TPCC_autogen/QFVAREFS7T/qrc_ThirdPartyResource.cpp", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/Source/TPCC_autogen/timestamp.rule", "sourceGroupIndex": 4}, {"backtrace": 0, "isGenerated": true, "path": "build/Source/TPCC_autogen/QFVAREFS7T/qrc_ThirdPartyResource.cpp.rule", "sourceGroupIndex": 4}], "type": "EXECUTABLE"}